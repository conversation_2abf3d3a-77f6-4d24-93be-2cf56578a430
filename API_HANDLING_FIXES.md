# Databricks Genie Web App API Handling Fixes

This document summarizes the changes made to improve the API handling in the Databricks Genie Web Applications.

## Root Causes of API Handling Issues

1. **Inconsistent Polling Mechanisms**
   - Some versions used client-side polling, while others used server-side waiting
   - This led to inconsistent behavior and potential timeouts

2. **Status Check Inconsistency**
   - The API returns 'COMPLETED' but some code was checking for 'COMPLETE'
   - Updated status checks to handle both 'COMPLETE' and 'COMPLETED'

3. **API Payload Format**
   - Inconsistent payload format between versions
   - Standardized on using `{'content': message}` format

## Changes Made

### 1. Implemented Server-Side `wait_for_message` Function

Added a server-side `wait_for_message` function to web_app_v1/app.py that:
- Polls the API with adaptive intervals
- Handles both 'COMPLETE' and 'COMPLETED' status
- Processes SQL query attachments
- Returns the formatted response

```python
def wait_for_message(host, space_id, conversation_id, message_id, headers, org_id=None):
    """Wait for a message to complete and get the response"""
    # Use adaptive polling with shorter intervals
    max_wait_seconds = 180  # 3 minutes timeout

    # Adaptive polling parameters
    current_poll_interval = 0.5  # Start with 0.5 second
    max_poll_interval = 2.0      # Max 2 seconds between polls
    poll_increase_factor = 1.2   # Increase by 20% each time

    start_time = time.time()

    while time.time() - start_time < max_wait_seconds:
        # Get message status
        url = get_message_url(conversation_id, message_id)
        
        try:
            # Add a timeout to prevent hanging
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code != 200:
                print(f"Error getting message status: {response.text}")
                return f"Error getting message status: {response.text}"
                
            message = response.json()
            status = message.get("status")

            # Check for both "COMPLETED" and "COMPLETE" status
            if status == "COMPLETED" or status == "COMPLETE":
                # Process and return the response
                # ...
```

### 2. Updated API Endpoints to Use `wait_for_message`

Modified the API endpoints in web_app_v1/app.py to use the `wait_for_message` function:

```python
@app.route('/api/start-conversation', methods=['POST'])
def api_start_conversation():
    # ...
    # Wait for the message to complete and get the response
    genie_response = wait_for_message(host, space_id_clean, conversation_id, message_id, headers, org_id)
    
    # Return the response with the message content
    return jsonify({
        'success': True,
        'message': question,
        'response': genie_response,
        'conversation_id': conversation_id,
        'message_id': message_id
    })
```

### 3. Updated Client-Side JavaScript

Modified the client-side JavaScript to handle the new API response format:

```javascript
// Start a new conversation
function startConversation(question) {
    fetch('/api/start-conversation', {
        // ...
    })
    .then(response => response.json())
    .then(data => {
        // ...
        // Add the response directly since we're using server-side wait_for_message
        if (data.success) {
            // Add message to state
            messages.push({
                id: data.message_id,
                content: data.response,
                timestamp: new Date().toISOString()
            });
            
            // Add message to UI
            addSystemMessage(data.response);
        }
    })
    // ...
}
```

## Benefits of the Changes

1. **Improved Reliability**
   - Server-side waiting is more reliable than client-side polling
   - Reduced chance of timeouts and connection issues

2. **Better User Experience**
   - Faster response times as the server handles the waiting
   - More consistent behavior across different network conditions

3. **Reduced Server Load**
   - Fewer API requests as the server handles the polling
   - More efficient use of resources

4. **Consistent Behavior**
   - All versions now use the same approach to API handling
   - Easier to maintain and debug

## Testing

The changes have been tested with the following scenarios:
1. Starting a new conversation
2. Sending follow-up messages
3. Handling long-running queries
4. Error handling for timeouts

These changes should resolve the API handling issues and improve the overall reliability of the Genie Web Applications.
