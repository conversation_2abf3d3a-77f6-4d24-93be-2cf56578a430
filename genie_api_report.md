# Databricks Genie API Exploration Report

## Summary

This report documents our exploration of the Databricks Genie API. We attempted to identify and test various API endpoints for interacting with Databricks Genie programmatically.

## Key Findings

1. **Web UI Access**: The Genie web UI is accessible at the URL:
   ```
   https://dbc-620d1468-0f52.cloud.databricks.com/genie/rooms/01f02f16a7b11b36a04e4353814a5699?o=1883526265026134
   ```

2. **API Endpoints**: We tested multiple potential API endpoints using various formats, but none were accessible through standard REST API calls. This suggests that the Genie API might not be publicly documented or might be using a different authentication mechanism.

3. **Authentication**: We attempted both OAuth and Personal Access Token (PAT) authentication methods, but neither provided access to any API endpoints.

## Tested API Formats

We tested the following API endpoint formats:

1. Standard REST API format:
   ```
   https://{host}/api/2.0/genie/rooms/{room_id}?o={org_id}
   ```

2. Direct API without 'rooms':
   ```
   https://{host}/api/2.0/genie/{room_id}?o={org_id}
   ```

3. API with 'datarooms':
   ```
   https://{host}/api/2.0/datarooms/{room_id}?o={org_id}
   ```

4. API without version:
   ```
   https://{host}/api/genie/rooms/{room_id}?o={org_id}
   ```

5. API for listing rooms/spaces:
   ```
   https://{host}/api/2.0/genie/rooms?o={org_id}
   https://{host}/api/2.0/genie/spaces?o={org_id}
   ```

None of these API formats returned a successful response.

## Recommendations for Further Investigation

1. **Browser Network Inspection**: Use browser developer tools to inspect network traffic while interacting with the Genie web UI. Look for XHR/Fetch requests that might reveal the actual API endpoints being used.

2. **WebSocket Communication**: Check if Genie uses WebSocket connections instead of REST API calls for real-time communication.

3. **Browser Automation**: Consider using browser automation tools like Selenium or Playwright to interact with the Genie web UI programmatically.

4. **Contact Databricks Support**: Reach out to Databricks support for official documentation on the Genie API, if available.

## Next Steps

1. Create a script to monitor and log network traffic while using the Genie web UI.
2. Implement a browser automation solution to interact with Genie programmatically.
3. Explore WebSocket connections for real-time communication with Genie.

## Code for Network Traffic Inspection

To inspect network traffic while using the Genie web UI, you can use the following approach:

1. Open the Genie web UI in your browser
2. Open the browser's developer tools (F12 or Ctrl+Shift+I)
3. Go to the Network tab
4. Filter for XHR/Fetch requests
5. Interact with Genie (send messages, etc.)
6. Look for API calls being made in the background

## Sample Code for Browser Automation

If you decide to use browser automation, here's a sample Python script using Selenium:

```python
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import json

# Configuration
DATABRICKS_HOST = "dbc-620d1468-0f52.cloud.databricks.com"
ROOM_ID = "01f02f16a7b11b36a04e4353814a5699"
ORG_ID = "1883526265026134"
USERNAME = "your_username"
PASSWORD = "your_password"

# Initialize the WebDriver
driver = webdriver.Chrome()  # or Firefox, Edge, etc.

try:
    # Navigate to the Databricks login page
    login_url = f"https://{DATABRICKS_HOST}/login"
    driver.get(login_url)
    
    # Wait for the login form to load
    username_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.ID, "username"))
    )
    
    # Enter credentials and login
    username_input.send_keys(USERNAME)
    driver.find_element(By.ID, "password").send_keys(PASSWORD)
    driver.find_element(By.ID, "login-button").click()
    
    # Wait for login to complete
    time.sleep(5)
    
    # Navigate to the Genie room
    genie_url = f"https://{DATABRICKS_HOST}/genie/rooms/{ROOM_ID}?o={ORG_ID}"
    driver.get(genie_url)
    
    # Wait for the Genie UI to load
    chat_input = WebDriverWait(driver, 10).until(
        EC.presence_of_element_located((By.CSS_SELECTOR, "textarea[placeholder='Ask a question...']"))
    )
    
    # Send a message
    chat_input.send_keys("What is Databricks Genie?")
    chat_input.submit()
    
    # Wait for the response
    time.sleep(5)
    
    # Extract the response
    response_elements = driver.find_elements(By.CSS_SELECTOR, ".message-content")
    for element in response_elements:
        print(element.text)
    
    # Keep the browser open for manual inspection
    input("Press Enter to close the browser...")
    
finally:
    driver.quit()
```

Note: You'll need to install the Selenium package (`pip install selenium`) and download the appropriate WebDriver for your browser.

## Conclusion

While the Genie web UI is accessible, we were unable to identify any publicly accessible API endpoints for programmatic interaction. Further investigation using browser network inspection and potentially browser automation is recommended to achieve programmatic interaction with Databricks Genie.
