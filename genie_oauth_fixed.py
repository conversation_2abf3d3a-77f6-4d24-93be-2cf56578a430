#!/usr/bin/env python3
"""
Databricks Genie API with OAuth Authentication (Fixed)

This script demonstrates how to use OAuth authentication with the Databricks Genie API
using the correct API URL formats.
"""

import os
import json
import time
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")

# Check if required environment variables are set
if not all([DATABRICKS_HOST, DATABRICKS_CLIENT_ID, DATABRICKS_CLIENT_SECRET, DATABRICKS_SPACE_ID]):
    print("Error: Missing required environment variables.")
    print("Please set DATABRICKS_HOST, DATABRICKS_CLIENT_ID, DATABRICKS_CLIENT_SECRET, and DATABRICKS_SPACE_ID in your .env file.")
    exit(1)

# Extract the space ID and org ID
space_id_full = DATABRICKS_SPACE_ID
space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full
org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

# Remove any 'datarooms/' prefix if it exists
if space_id.startswith('datarooms/'):
    space_id = space_id.replace('datarooms/', '')

# Token cache for OAuth
_token_cache = {
    "access_token": None,
    "expires_at": 0
}

def get_oauth_token():
    """Get an OAuth token for authenticating with the Databricks API"""
    global _token_cache

    # Check if we have a valid cached token
    current_time = time.time()
    if _token_cache["access_token"] and _token_cache["expires_at"] > current_time + 60:
        return _token_cache["access_token"]

    # No valid token, request a new one
    # Workspace-level token endpoint
    token_endpoint = f"https://{DATABRICKS_HOST}/oidc/v1/token"

    # Request the token
    response = requests.post(
        token_endpoint,
        auth=(DATABRICKS_CLIENT_ID, DATABRICKS_CLIENT_SECRET),
        data={"grant_type": "client_credentials", "scope": "all-apis"}
    )

    # Check if the request was successful
    if response.status_code != 200:
        raise Exception(f"Failed to get OAuth token: {response.text}")

    # Cache the token
    token_data = response.json()
    _token_cache["access_token"] = token_data["access_token"]
    _token_cache["expires_at"] = current_time + token_data["expires_in"]

    return _token_cache["access_token"]

def get_oauth_headers():
    """Get headers for OAuth authentication"""
    token = get_oauth_token()
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def get_genie_space():
    """Get information about a specific Genie space using OAuth authentication"""
    print("\n=== Getting Genie Space Information ===")

    # Get OAuth headers
    headers = get_oauth_headers()

    # Use the correct URL format for accessing a specific Genie space
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}"

    print(f"GET {url}")

    try:
        response = requests.get(url, headers=headers)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✅ Success!")
            data = response.json()
            print(f"Space ID: {data.get('space_id')}")
            print(f"Title: {data.get('title')}")
            print(f"Description: {data.get('description', 'No description')[:100]}...")
            return data
        else:
            print("❌ Failed!")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def start_conversation(question):
    """Start a new conversation in a Genie space using OAuth authentication"""
    print(f"\n=== Starting Conversation with Question: '{question}' ===")

    # Get OAuth headers
    headers = get_oauth_headers()

    # Use the correct URL format for starting a conversation
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation"

    # Prepare the payload
    payload = {"content": question}

    print(f"POST {url}")
    print(f"Payload: {json.dumps(payload)}")

    try:
        response = requests.post(url, headers=headers, json=payload)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✅ Success!")
            data = response.json()
            print(f"Message ID: {data.get('message_id')}")
            print(f"Conversation ID: {data.get('conversation_id')}")
            return data
        else:
            print("❌ Failed!")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def get_message(conversation_id, message_id):
    """Get a message from a conversation using OAuth authentication"""
    print(f"\n=== Getting Message ===")
    print(f"Conversation ID: {conversation_id}")
    print(f"Message ID: {message_id}")

    # Get OAuth headers
    headers = get_oauth_headers()

    # Use the correct URL format for getting a message
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}"

    print(f"GET {url}")

    try:
        response = requests.get(url, headers=headers)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✅ Success!")
            data = response.json()

            # Check if the message is still processing
            status = data.get("status")
            print(f"Message Status: {status}")

            if status == "COMPLETED":
                print("Message is complete!")
                # Check for response content in different possible locations
                response_content = None

                # Try to get response content from the response field
                if "response" in data and isinstance(data["response"], dict):
                    response_content = data["response"].get("content")

                # If not found, try to get it from the content field
                if not response_content and "content" in data:
                    response_content = data["content"]

                # If still not found, try to get it from the message field
                if not response_content and "message" in data and isinstance(data["message"], dict):
                    response_content = data["message"].get("content")

                if response_content:
                    print(f"\nResponse Content: {response_content[:200]}...")
                else:
                    print("No response content found in the completed message")
            elif status in ["SUBMITTED", "PROCESSING", "ASKING_AI", "PENDING_WAREHOUSE"]:
                print(f"Message is still processing (status: {status})...")
            else:
                print(f"Unknown status: {status}")

            return data
        else:
            print("❌ Failed!")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return None

def wait_for_message_completion(conversation_id, message_id, max_wait_seconds=120, poll_interval=2):
    """Wait for a message to complete processing"""
    print(f"\n=== Waiting for Message Completion ===")
    print(f"Conversation ID: {conversation_id}")
    print(f"Message ID: {message_id}")
    print(f"Maximum Wait Time: {max_wait_seconds} seconds")
    print(f"Poll Interval: {poll_interval} seconds")

    start_time = time.time()
    elapsed_time = 0

    while elapsed_time < max_wait_seconds:
        # Get the message
        message_data = get_message(conversation_id, message_id)

        if not message_data:
            print("❌ Failed to get message data")
            return None

        # Check if the message is complete
        status = message_data.get("status")

        if status == "COMPLETED":
            print(f"✅ Message completed after {elapsed_time:.1f} seconds")
            return message_data
        elif status in ["SUBMITTED", "PROCESSING", "ASKING_AI", "PENDING_WAREHOUSE"]:
            print(f"Message is still processing (status: {status})... ({elapsed_time:.1f} seconds elapsed)")
            time.sleep(poll_interval)
            elapsed_time = time.time() - start_time
        else:
            print(f"⚠️ Unknown status: {status}")
            # Continue waiting in case it's a new status we don't recognize
            time.sleep(poll_interval)
            elapsed_time = time.time() - start_time

    print(f"❌ Timed out after {max_wait_seconds} seconds")
    return message_data  # Return the last message data even if timed out

def main():
    """Main function"""
    print("=== Databricks Genie API with OAuth Authentication (Fixed) ===")
    print(f"Host: {DATABRICKS_HOST}")
    print(f"Space ID: {space_id}")
    print(f"Organization ID: {org_id}")

    # Get OAuth token
    try:
        token = get_oauth_token()
        print(f"\n✅ Successfully obtained OAuth token: {token[:10]}...")
    except Exception as e:
        print(f"\n❌ Failed to get OAuth token: {str(e)}")
        return

    # Get Genie space information
    space_data = get_genie_space()

    if not space_data:
        print("\n❌ Failed to get Genie space information")
        return

    # Start a conversation
    question = "Show me the top 10 customers by revenue"
    conversation_data = start_conversation(question)

    if not conversation_data:
        print("\n❌ Failed to start conversation")
        return

    # Extract conversation ID and message ID
    conversation_id = conversation_data.get("conversation_id")
    message_id = conversation_data.get("message_id")

    if not conversation_id or not message_id:
        print("\n❌ Failed to get conversation ID or message ID")
        return

    # Wait for the message to complete
    completed_message = wait_for_message_completion(conversation_id, message_id)

    if not completed_message:
        print("\n❌ Failed to get completed message")
        return

    # Extract and display the response
    response_content = None

    # Try to get response content from different possible locations
    if "response" in completed_message and isinstance(completed_message["response"], dict):
        response_content = completed_message["response"].get("content")

    if not response_content and "content" in completed_message:
        response_content = completed_message["content"]

    if not response_content and "message" in completed_message and isinstance(completed_message["message"], dict):
        response_content = completed_message["message"].get("content")

    # If we still don't have response content, try to extract it from the SQL results
    if not response_content and "response" in completed_message:
        response_obj = completed_message["response"]
        if isinstance(response_obj, dict) and "sql_results" in response_obj:
            sql_results = response_obj["sql_results"]
            if isinstance(sql_results, list) and len(sql_results) > 0:
                response_content = "SQL Results:\n"
                for result in sql_results:
                    response_content += json.dumps(result, indent=2) + "\n"

    if response_content:
        print("\n=== Genie Response ===")
        print(response_content)
    else:
        print("\n❌ No response content found")
        print("Raw response data:")
        print(json.dumps(completed_message, indent=2))

if __name__ == "__main__":
    main()
