#!/usr/bin/env python3
"""
Test Different Databricks Genie API URL Formats

This script tests various URL formats for the Databricks Genie API
to find the correct one for your environment.
"""

import os
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")  # PAT for testing
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")

# Check if required environment variables are set
if not DATABRICKS_HOST or not DATABRICKS_TOKEN:
    print("Error: Missing required environment variables.")
    print("Please set DATABRICKS_HOST and DATABRICKS_TOKEN in your .env file.")
    exit(1)

# Extract the space ID and org ID
space_id_full = DATABRICKS_SPACE_ID if DATABRICKS_SPACE_ID else "01f02f16a7b11b36a04e4353814a5699?o=1883526265026134"
space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full
org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

# Remove any 'datarooms/' prefix if it exists
if space_id.startswith('datarooms/'):
    space_id = space_id.replace('datarooms/', '')

def get_headers():
    """Get headers for PAT authentication"""
    return {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }

def test_url(name, url, method="GET", data=None):
    """Test a URL and print the result"""
    print(f"\n=== Testing {name} ===")
    print(f"{method} {url}")
    
    headers = get_headers()
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        else:
            print(f"Unsupported method: {method}")
            return
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Success!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)[:500]}...")
            except:
                print(f"Response: {response.text[:500]}...")
        else:
            print("❌ Failed!")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {str(e)}")

def main():
    """Main function"""
    print("=== Testing Different Databricks Genie API URL Formats ===")
    print(f"Host: {DATABRICKS_HOST}")
    print(f"Space ID: {space_id}")
    print(f"Organization ID: {org_id}")
    
    # Test various URL formats for listing Genie spaces
    list_spaces_formats = [
        # Format 1: Standard format
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces",
        
        # Format 2: With datarooms
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms",
        
        # Format 3: Just genie
        f"https://{DATABRICKS_HOST}/api/2.0/genie?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie",
        
        # Format 4: With rooms
        f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms",
        
        # Format 5: With datarooms/list
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/list?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/list",
    ]
    
    print("\n--- Testing URLs for Listing Genie Spaces ---")
    for i, url in enumerate(list_spaces_formats):
        test_url(f"List Spaces Format {i+1}", url)
    
    # Test various URL formats for accessing a specific Genie space
    space_formats = [
        # Format 1: Standard format
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}",
        
        # Format 2: With datarooms
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}",
        
        # Format 3: With rooms
        f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms/{space_id}",
        
        # Format 4: With datarooms and get
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/get/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/get/{space_id}",
        
        # Format 5: With datarooms and get
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/get?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/get",
    ]
    
    print("\n--- Testing URLs for Accessing a Specific Genie Space ---")
    for i, url in enumerate(space_formats):
        test_url(f"Space Format {i+1}", url)
    
    # Test various URL formats for starting a conversation
    conversation_formats = [
        # Format 1: Standard format
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation",
        
        # Format 2: With datarooms
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}/start-conversation?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}/start-conversation",
        
        # Format 3: With rooms
        f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms/{space_id}/start-conversation?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms/{space_id}/start-conversation",
        
        # Format 4: With conversations
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/start?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/start",
        
        # Format 5: With datarooms and conversations
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}/conversations/start?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}/conversations/start",
    ]
    
    print("\n--- Testing URLs for Starting a Conversation ---")
    data = {"content": "Show me the top 10 customers by revenue"}
    for i, url in enumerate(conversation_formats):
        test_url(f"Start Conversation Format {i+1}", url, "POST", data)

if __name__ == "__main__":
    main()
