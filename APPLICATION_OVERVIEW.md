# Databricks Genie Web Application - Complete Overview

## Core Application Files

### 1. Original Web App (web_app/)
**Main Files:**
- `web_app/app.py` - Main Flask application with PAT and OAuth authentication
- `web_app/templates/` - HTML templates for the UI
- `web_app/static/` - CSS, JS, and image assets

**Key Endpoints:**
- `/` - Landing page (redirects to login)
- `/login` - Login page (supports both PAT and OAuth)
- `/login/<auth_type>` - Specific authentication type login
- `/conversation` - Main conversation interface
- `/api/send-message` - Send message to Genie API
- `/api/start-conversation` - Start new conversation
- `/api/new_conversation` - Clear current conversation
- `/api/switch-auth` - Switch between PAT/OAuth authentication
- `/api/get-message` - Get message status

**Authentication Methods:**
- PAT (Personal Access Token)
- OAuth (Service Principal)

### 2. Web App Version 1 (web_app_v1/)
**Main Files:**
- `web_app_v1/app.py` - OAuth Federation with U2M authentication
- `web_app_v1/auth_federation.py` - OAuth federation logic
- `web_app_v1/auth_routes.py` - Authentication routes
- `web_app_v1/admin_routes.py` - Admin dashboard routes
- `web_app_v1/models.py` - Database models for users and workspaces
- `web_app_v1/oauth_auth.py` - OAuth authentication utilities
- `web_app_v1/okta_auth.py` - Okta integration
- `web_app_v1/config.yaml` - Configuration file

**Key Features:**
- SSO-style login with email/password
- Admin/User role-based access control
- JWT token creation and OAuth federation
- Individual user identity preservation
- Multi-workspace support

**Key Endpoints:**
- `/` - Landing page (redirects to login)
- `/login` - SSO-style login page
- `/admin/dashboard` - Admin dashboard
- `/admin/workspaces` - Workspace management
- `/admin/users` - User management
- `/conversation` - Conversation interface
- `/api/send-message` - Send message to Genie
- `/api/federation/token` - Get federation token

### 3. Web App Version 2 (web_app_v2/)
**Main Files:**
- `web_app_v2/app.py` - Config file based authentication (no login UI)
- `web_app_v2/oauth_auth.py` - OAuth utilities
- `web_app_v2/config.yaml` - Configuration file

**Key Features:**
- No login screen - uses config file
- Automatic authentication on startup
- Direct conversation interface

**Key Endpoints:**
- `/` - Auto-authenticate and redirect to conversation
- `/conversation` - Main conversation interface
- `/api/send-message` - Send message to Genie

### 4. Web App Version 3 (web_app_v3/)
**Main Files:**
- `web_app_v3/demo_app.py` - Demo application
- `web_app_v3/genie_auth.py` - Authentication library
- `web_app_v3/config.yaml` - Configuration file

**Key Features:**
- Authentication library/component only
- Reusable authentication module
- Token caching and management
- Simple demo interface

**Key Endpoints:**
- `/` - Demo landing page
- `/api/test-auth` - Test authentication
- `/api/token-info` - Get token information

## Configuration Files

### Common Configuration Structure:
```yaml
auth:
  host: "your-databricks-host"
  space_id: "your-genie-space-id"
  client_id: "oauth-client-id"
  client_secret: "oauth-client-secret"
  account_id: "databricks-account-id"

ui:
  title: "Databricks Genie"
  colors:
    primary: "#FF3621"
    secondary: "#1B3139"
  logos:
    databricks: "databricks.png"
    tudip: "tudip.jpeg"

flask:
  secret_key: "your-secret-key"
  debug: false
  host: "0.0.0.0"
  port: 5001
```

## Dependencies (requirements.txt)
- `requests>=2.25.0` - HTTP requests
- `python-dotenv>=0.15.0` - Environment variables
- `pandas>=1.0.0` - Data manipulation
- `tabulate>=0.8.7` - Table formatting
- `pillow>=9.0.0` - Image processing
- `flask>=2.0.0` - Web framework
- `flask-bootstrap4>=4.0.2` - Bootstrap integration
- `flask-wtf>=1.0.0` - Form handling
- `werkzeug>=2.0.0` - WSGI utilities
- `pyyaml>=6.0.0` - YAML parsing

## Assets (assets/)
- `databricks.png` - Databricks logo
- `databricks_logo.png` - Alternative Databricks logo
- `databricksicon.png` - Databricks icon
- `tudip.jpeg` - Tudip logo
- `favicon.ico` - Favicon
- `databricks_theme.css` - Databricks theme styles

## Database (for web_app_v1)
- `web_app_v1/instance/genie_federation.db` - SQLite database for users and workspaces
- Tables: users, workspaces, user_workspace_access

## Key Authentication Flows

### Original Web App:
1. User selects PAT or OAuth authentication
2. Enters credentials on login page
3. System validates credentials with Databricks
4. User proceeds to conversation interface

### Web App V1 (Federation):
1. User logs in with email/password (SSO-style)
2. System creates JWT token for user
3. JWT is exchanged for Databricks OAuth token via federation
4. Individual user identity preserved in Databricks audit logs

### Web App V2:
1. System reads credentials from config file
2. Auto-authenticates on startup
3. Direct access to conversation interface

### Web App V3:
1. Library provides authentication utilities
2. Applications integrate the GenieAuth class
3. Handles token caching and renewal automatically
