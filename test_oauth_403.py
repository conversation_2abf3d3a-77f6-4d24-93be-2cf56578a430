#!/usr/bin/env python3
"""
Test OAuth Authentication with Databricks Genie API

This script tests OAuth authentication with the Databricks Genie API
to reproduce the 403 permission denied error.
"""

import os
import json
import requests
from dotenv import load_dotenv

# Import OAuth authentication module
try:
    from oauth_auth import get_auth_headers
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")

# Check if required environment variables are set
if not DATABRICKS_HOST:
    print("Error: Missing required environment variables.")
    print("Please set DATABRICKS_HOST in your .env file.")
    exit(1)

if not OAUTH_AVAILABLE or not DATABRICKS_CLIENT_ID or not DATABRICKS_CLIENT_SECRET:
    print("Error: OAuth authentication not available.")
    print("Please set DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET in your .env file.")
    exit(1)

def test_oauth_auth():
    """Test OAuth authentication with the Databricks Genie API"""
    print("Testing OAuth authentication with Databricks Genie API")
    print(f"Host: {DATABRICKS_HOST}")

    # Get OAuth headers
    headers = get_auth_headers()

    # Test with a simple API call first
    url = f"https://{DATABRICKS_HOST}/api/2.0/clusters/list"
    print(f"\nTesting OAuth token with standard API call to: {url}")

    try:
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            print("Standard API call successful!")
            print(f"Response: {json.dumps(response.json(), indent=2)[:200]}...")
        else:
            print(f"Standard API call failed with status code {response.status_code}")
            print(f"Response: {response.text}")
            return
    except Exception as e:
        print(f"Error with standard API call: {str(e)}")
        return

    # Now test with the Genie API using the exact same approach as in genie_api_explorer.py
    # This is the approach that generated the log file with the 403 error

    # Try a different space ID format
    space_id_full = "01f02f16a7b11b36a04e4353814a5699?o=1883526265026134"
    print(f"\nSpace ID: {space_id_full}")

    # Extract the space ID without any query parameters
    space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full

    # Extract the organization ID
    org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

    print(f"Extracted Space ID: {space_id}")
    print(f"Extracted Org ID: {org_id}")

    # Use the space_id directly without any prefix
    print(f"Using Space ID directly: {space_id}")

    # Use the datarooms URL format that returns a 403 error
    if org_id:
        url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms?o={org_id}"
    else:
        url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms"

    print(f"\nTesting OAuth token with Genie API spaces endpoint: {url}")

    try:
        response = requests.get(url, headers=headers)

        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("Genie API spaces call successful!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except json.JSONDecodeError:
                print(f"Response is not in JSON format: {response.text[:200]}...")
        else:
            print(f"Genie API spaces call failed with status code {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error with Genie API spaces call: {str(e)}")

    # Now try the start-conversation endpoint with datarooms format
    if org_id:
        url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/start-conversation?o={org_id}"
    else:
        url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/start-conversation"

    print(f"\nTesting OAuth token with Genie API start-conversation endpoint: {url}")

    # Use POST for start-conversation endpoint
    payload = {"content": "Show me the top 10 customers by revenue"}

    try:
        response = requests.post(url, headers=headers, json=payload)

        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("Genie API call successful!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except json.JSONDecodeError:
                print(f"Response is not in JSON format: {response.text[:200]}...")
        else:
            print(f"Genie API call failed with status code {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error with Genie API call: {str(e)}")

if __name__ == "__main__":
    test_oauth_auth()
