#!/usr/bin/env python3
"""
Databricks Genie API Tester

This script tests the Databricks Genie API using the correct URL format:
https://{host}/genie/rooms/{room_id}?o={org_id}

Usage:
    python test_genie_api.py

Requirements:
    - Python 3.6+
    - requests
    - python-dotenv
"""

import os
import sys
import json
import requests
from dotenv import load_dotenv

# Import OAuth authentication module
try:
    from oauth_auth import get_auth_headers
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables from .env file
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")

# Check if required environment variables are set
if not DATABRICKS_HOST or not DATABRICKS_SPACE_ID:
    print("Error: Missing required environment variables.")
    print("Please create a .env file with DATABRICKS_HOST and DATABRICKS_SPACE_ID.")
    sys.exit(1)

# Check authentication method
USE_OAUTH = OAUTH_AVAILABLE and DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET
if not USE_OAUTH and not DATABRICKS_TOKEN:
    print("Error: No authentication method available.")
    print("Please provide either a personal access token (DATABRICKS_TOKEN) or OAuth credentials (DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET).")
    sys.exit(1)

# Get headers for API requests
if USE_OAUTH:
    print("Using OAuth authentication")
    HEADERS = get_auth_headers()
else:
    print("Using Personal Access Token (PAT) authentication")
    HEADERS = {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }

# Extract the space ID without any query parameters
ROOM_ID = DATABRICKS_SPACE_ID.split('?')[0] if '?' in DATABRICKS_SPACE_ID else DATABRICKS_SPACE_ID
ORG_ID = None

# Check if there's an organization ID in the space ID
if '?' in DATABRICKS_SPACE_ID and 'o=' in DATABRICKS_SPACE_ID:
    ORG_ID = DATABRICKS_SPACE_ID.split('o=')[1] if 'o=' in DATABRICKS_SPACE_ID else None

# Remove any 'datarooms/' prefix if it exists in the ROOM_ID
if ROOM_ID.startswith('datarooms/'):
    ROOM_ID = ROOM_ID.replace('datarooms/', '')

def try_api_endpoint(endpoint_path, method="GET", data=None):
    """Try a Genie API endpoint and return the response"""
    # Construct the base URL with the correct format
    base_url = f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms/{ROOM_ID}"

    # Add the endpoint path if provided
    if endpoint_path:
        if endpoint_path.startswith('/'):
            url = f"{base_url}{endpoint_path}"
        else:
            url = f"{base_url}/{endpoint_path}"
    else:
        url = base_url

    # Add organization ID if available
    if ORG_ID:
        url = f"{url}?o={ORG_ID}"

    print(f"\nTrying {method} {url}")

    try:
        if method == "GET":
            response = requests.get(url, headers=HEADERS)
        elif method == "POST":
            response = requests.post(url, headers=HEADERS, json=data)
        else:
            print(f"Unsupported method: {method}")
            return None

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✓ Success!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Response: {response.text}")
            return True
        else:
            print(f"✗ Failed: {response.text}")
            return False
    except Exception as e:
        print(f"✗ Exception: {str(e)}")
        return False

def try_web_api_endpoint():
    """Try the web UI API endpoint directly"""
    url = f"https://{DATABRICKS_HOST}/genie/rooms/{ROOM_ID}"
    if ORG_ID:
        url = f"{url}?o={ORG_ID}"

    print(f"\nTrying direct web UI access: GET {url}")

    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("✓ Success! (This is the web UI endpoint, not an API endpoint)")
            # Try to extract any useful information from the HTML response
            html_content = response.text
            if len(html_content) > 200:
                print(f"HTML Content (first 200 chars): {html_content[:200]}...")
            else:
                print(f"HTML Content: {html_content}")
            return True
        else:
            print(f"✗ Failed: {response.text[:200]}...")
            return False
    except Exception as e:
        print(f"✗ Exception: {str(e)}")
        return False

def main():
    """Main function"""
    print("Databricks Genie API Tester")
    print(f"Host: {DATABRICKS_HOST}")
    print(f"Room ID: {ROOM_ID}")
    print(f"Organization ID: {ORG_ID}")
    print(f"Authentication Method: {'OAuth' if USE_OAUTH else 'Personal Access Token (PAT)'}")

    print("\nTesting Genie API endpoints...")

    # Try the web UI endpoint first
    web_ui_success = try_web_api_endpoint()

    # Try various API endpoints
    endpoints = [
        "",  # Base endpoint
        "messages",
        "conversations",
        "start",
        "query",
        "chat",
        "status",
        "info"
    ]

    success_count = 0
    for endpoint in endpoints:
        # Try GET request
        if try_api_endpoint(endpoint, "GET"):
            success_count += 1

        # Try POST request with a simple payload for some endpoints
        if endpoint in ["messages", "query", "chat"]:
            payload = {"content": "What is Databricks Genie?"}
            if try_api_endpoint(endpoint, "POST", payload):
                success_count += 1

    # Try alternative API formats
    print("\nTrying alternative API formats...")

    # Format 1: Direct API without 'rooms'
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/{ROOM_ID}"
    if ORG_ID:
        url = f"{url}?o={ORG_ID}"

    print(f"\nTrying GET {url}")
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("✓ Success!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Response: {response.text}")
            success_count += 1
        else:
            print(f"✗ Failed: {response.text}")
    except Exception as e:
        print(f"✗ Exception: {str(e)}")

    # Format 2: API with 'datarooms'
    url = f"https://{DATABRICKS_HOST}/api/2.0/datarooms/{ROOM_ID}"
    if ORG_ID:
        url = f"{url}?o={ORG_ID}"

    print(f"\nTrying GET {url}")
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("✓ Success!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Response: {response.text}")
            success_count += 1
        else:
            print(f"✗ Failed: {response.text}")
    except Exception as e:
        print(f"✗ Exception: {str(e)}")

    # Format 3: Try the API format based on the web UI URL
    print("\nTrying API formats based on the web UI URL...")

    # Try with /api prefix
    url = f"https://{DATABRICKS_HOST}/api/genie/rooms/{ROOM_ID}"
    if ORG_ID:
        url = f"{url}?o={ORG_ID}"

    print(f"\nTrying GET {url}")
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("✓ Success!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Response: {response.text}")
            success_count += 1
        else:
            print(f"✗ Failed: {response.text}")
    except Exception as e:
        print(f"✗ Exception: {str(e)}")

    # Try with /api/2.0 prefix
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms/{ROOM_ID}"
    if ORG_ID:
        url = f"{url}?o={ORG_ID}"

    print(f"\nTrying GET {url}")
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("✓ Success!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Response: {response.text}")
            success_count += 1
        else:
            print(f"✗ Failed: {response.text}")
    except Exception as e:
        print(f"✗ Exception: {str(e)}")

    # Try with /api/2.0/genie/rooms
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms"
    if ORG_ID:
        url = f"{url}?o={ORG_ID}"

    print(f"\nTrying GET {url}")
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("✓ Success!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Response: {response.text}")
            success_count += 1
        else:
            print(f"✗ Failed: {response.text}")
    except Exception as e:
        print(f"✗ Exception: {str(e)}")

    # Try with /api/2.0/genie/spaces
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces"
    if ORG_ID:
        url = f"{url}?o={ORG_ID}"

    print(f"\nTrying GET {url}")
    try:
        response = requests.get(url, headers=HEADERS)
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("✓ Success!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except:
                print(f"Response: {response.text}")
            success_count += 1
        else:
            print(f"✗ Failed: {response.text}")
    except Exception as e:
        print(f"✗ Exception: {str(e)}")

    if success_count > 0:
        print(f"\n✓ Found {success_count} working API endpoints!")
        print("Check the output above for details on which endpoints worked.")
    elif web_ui_success:
        print("\n✓ The web UI is accessible, but no API endpoints were found.")
        print("Recommendations:")
        print("1. The Genie web UI is accessible, but the API may not be publicly documented")
        print("2. Consider using browser automation or web scraping techniques")
        print("3. Try inspecting network traffic in your browser's developer tools while using Genie")
        print("4. Look for XHR/Fetch requests in the Network tab when interacting with Genie")
        print("5. Contact Databricks support for official API documentation")
    else:
        print("\n✗ No working endpoints found (neither web UI nor API).")
        print("Recommendations:")
        print("1. Check your authentication credentials")
        print("2. Verify the Room ID and Organization ID")
        print("3. Contact your Databricks administrator for help")

if __name__ == "__main__":
    main()
