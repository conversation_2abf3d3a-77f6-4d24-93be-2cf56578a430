#!/usr/bin/env python3
"""
Databricks Genie Web Application Launcher

This script provides a menu to launch different versions of the Databricks Genie Web Application.
It checks for required dependencies and installs them if needed.

Usage:
    python run_web_app.py
"""

import os
import sys
import subprocess
import webbrowser
import time

def check_dependencies():
    """Check if required packages are installed"""
    missing_deps = []

    try:
        import flask
    except ImportError:
        missing_deps.append("flask")

    try:
        import flask_bootstrap
    except ImportError:
        missing_deps.append("flask-bootstrap4")

    try:
        import flask_wtf
    except ImportError:
        missing_deps.append("flask-wtf")

    try:
        import yaml
    except ImportError:
        missing_deps.append("pyyaml")

    try:
        import requests
    except ImportError:
        missing_deps.append("requests")

    try:
        from dotenv import load_dotenv
    except ImportError:
        missing_deps.append("python-dotenv")

    if missing_deps:
        print("The following dependencies are missing:")
        for dep in missing_deps:
            print(f"  - {dep}")
        return False

    return True

def install_dependencies():
    """Install required packages"""
    print("Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("Dependencies installed successfully.")
    except subprocess.CalledProcessError:
        print("\nError: Could not install dependencies using pip.")
        print("This might be due to system restrictions or an externally managed environment.")
        print("\nPlease install the required packages manually:")
        print("  - flask")
        print("  - flask-bootstrap4")
        print("  - flask-wtf")
        print("  - pyyaml")
        print("  - requests")
        print("  - python-dotenv")
        print("\nYou can create a virtual environment and install the packages there:")
        print("  python3 -m venv venv")
        print("  source venv/bin/activate")
        print("  pip install flask flask-bootstrap4 flask-wtf pyyaml requests python-dotenv")
        print("\nOr use your system's package manager:")
        print("  sudo apt install python3-flask python3-yaml python3-requests python3-dotenv")

        choice = input("\nDo you want to continue without installing dependencies? (y/n): ")
        if choice.lower() != 'y':
            print("Exiting...")
            sys.exit(1)

def run_original_web_app():
    """Run the original web application"""
    print("\nStarting Original Web Application...")

    # Set environment variable for Flask
    os.environ["FLASK_APP"] = "web_app/app.py"

    # Start the Flask application
    process = subprocess.Popen(
        [sys.executable, "-m", "flask", "run", "--host=0.0.0.0", "--port=5000"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        universal_newlines=True
    )

    # Wait for the server to start
    time.sleep(2)

    # Open the web browser
    webbrowser.open("http://localhost:5000")

    print("Web application is running at http://localhost:5000")
    print("Press Ctrl+C to stop the server.")

    try:
        # Keep the script running
        while True:
            output = process.stdout.readline()
            if output:
                print(output.strip())

            error = process.stderr.readline()
            if error:
                print(f"ERROR: {error.strip()}", file=sys.stderr)

            # Check if process is still running
            if process.poll() is not None:
                break
    except KeyboardInterrupt:
        print("\nStopping the server...")
        process.terminate()
        process.wait()
        print("Server stopped.")

def run_version_1():
    """Run Version 1: Web App with OAuth Login Screen"""
    print("\nStarting Version 1: Web App with OAuth Login Screen...")

    # Change to the version 1 directory
    if os.path.exists("web_app_v1"):
        # Set environment variable for Flask
        os.environ["FLASK_APP"] = "web_app_v1/app.py"

        # Start the Flask application
        process = subprocess.Popen(
            [sys.executable, "-m", "flask", "run", "--host=0.0.0.0", "--port=5000"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )

        # Wait for the server to start
        time.sleep(2)

        # Open the web browser
        webbrowser.open("http://localhost:5000")

        print("Web application is running at http://localhost:5000")
        print("Press Ctrl+C to stop the server.")

        try:
            # Keep the script running
            while True:
                output = process.stdout.readline()
                if output:
                    print(output.strip())

                error = process.stderr.readline()
                if error:
                    print(f"ERROR: {error.strip()}", file=sys.stderr)

                # Check if process is still running
                if process.poll() is not None:
                    break
        except KeyboardInterrupt:
            print("\nStopping the server...")
            process.terminate()
            process.wait()
            print("Server stopped.")
    else:
        print(f"Error: web_app_v1 directory not found.")
        print("Please make sure you have copied the web_app_v1 directory from the Versions folder.")

def run_version_2():
    """Run Version 2: Web App with Config File (No Login Screen)"""
    print("\nStarting Version 2: Web App with Config File (No Login Screen)...")

    # Check if the version 2 directory exists
    if os.path.exists("web_app_v2"):
        # Set environment variable for Flask
        os.environ["FLASK_APP"] = "web_app_v2/app.py"

        # Start the Flask application
        process = subprocess.Popen(
            [sys.executable, "-m", "flask", "run", "--host=0.0.0.0", "--port=5000"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )

        # Wait for the server to start
        time.sleep(2)

        # Open the web browser
        webbrowser.open("http://localhost:5000")

        print("Web application is running at http://localhost:5000")
        print("Press Ctrl+C to stop the server.")

        try:
            # Keep the script running
            while True:
                output = process.stdout.readline()
                if output:
                    print(output.strip())

                error = process.stderr.readline()
                if error:
                    print(f"ERROR: {error.strip()}", file=sys.stderr)

                # Check if process is still running
                if process.poll() is not None:
                    break
        except KeyboardInterrupt:
            print("\nStopping the server...")
            process.terminate()
            process.wait()
            print("Server stopped.")
    else:
        print(f"Error: web_app_v2 directory not found.")
        print("Please make sure you have copied the web_app_v2 directory from the Versions folder.")

def run_version_3():
    """Run Version 3: Authentication Only Web Component"""
    print("\nStarting Version 3: Authentication Only Web Component...")

    # Check if the version 3 directory exists
    if os.path.exists("web_app_v3"):
        # Set environment variable for Flask
        os.environ["FLASK_APP"] = "web_app_v3/demo_app.py"

        # Start the Flask application
        process = subprocess.Popen(
            [sys.executable, "-m", "flask", "run", "--host=0.0.0.0", "--port=5000"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )

        # Wait for the server to start
        time.sleep(2)

        # Open the web browser
        webbrowser.open("http://localhost:5000")

        print("Web application is running at http://localhost:5000")
        print("Press Ctrl+C to stop the server.")

        try:
            # Keep the script running
            while True:
                output = process.stdout.readline()
                if output:
                    print(output.strip())

                error = process.stderr.readline()
                if error:
                    print(f"ERROR: {error.strip()}", file=sys.stderr)

                # Check if process is still running
                if process.poll() is not None:
                    break
        except KeyboardInterrupt:
            print("\nStopping the server...")
            process.terminate()
            process.wait()
            print("Server stopped.")
    else:
        print(f"Error: web_app_v3 directory not found.")
        print("Please make sure you have copied the web_app_v3 directory from the Versions folder.")

def show_menu():
    """Show the menu to select a version to run"""
    print("\nDatabricks Genie Web Application Launcher")
    print("=========================================")
    print("\nSelect a version to run:")
    print("1. Version 1: Web App with OAuth Login Screen")
    print("2. Version 2: Web App with Config File (No Login Screen)")
    print("3. Version 3: Authentication Only Web Component")
    print("4. Original Web App (with PAT and OAuth)")
    print("0. Exit")

    choice = input("\nEnter your choice (0-4): ")

    if choice == "1":
        run_version_1()
    elif choice == "2":
        run_version_2()
    elif choice == "3":
        run_version_3()
    elif choice == "4":
        run_original_web_app()
    elif choice == "0":
        print("\nExiting...")
        sys.exit(0)
    else:
        print("\nInvalid choice. Please try again.")
        show_menu()

def copy_version_folders():
    """Copy the web_app_v* folders from Versions to the root directory if they don't exist"""
    # Check if Versions folder exists
    if not os.path.exists("Versions"):
        print("Versions folder not found. Skipping copy operation.")
        return

    # Check and copy web_app_v1
    if not os.path.exists("web_app_v1") and os.path.exists("Versions/web_app_v1"):
        print("Copying web_app_v1 from Versions folder...")
        try:
            subprocess.check_call(["cp", "-r", "Versions/web_app_v1", "."])
            print("web_app_v1 copied successfully.")
        except subprocess.CalledProcessError:
            print("Error copying web_app_v1. Please copy it manually.")

    # Check and copy web_app_v2
    if not os.path.exists("web_app_v2") and os.path.exists("Versions/web_app_v2"):
        print("Copying web_app_v2 from Versions folder...")
        try:
            subprocess.check_call(["cp", "-r", "Versions/web_app_v2", "."])
            print("web_app_v2 copied successfully.")
        except subprocess.CalledProcessError:
            print("Error copying web_app_v2. Please copy it manually.")

    # Check and copy web_app_v3
    if not os.path.exists("web_app_v3") and os.path.exists("Versions/web_app_v3"):
        print("Copying web_app_v3 from Versions folder...")
        try:
            subprocess.check_call(["cp", "-r", "Versions/web_app_v3", "."])
            print("web_app_v3 copied successfully.")
        except subprocess.CalledProcessError:
            print("Error copying web_app_v3. Please copy it manually.")

    # Check and copy oauth_auth.py if it doesn't exist in the root
    if not os.path.exists("oauth_auth.py") and os.path.exists("Versions/oauth_auth.py"):
        print("Copying oauth_auth.py from Versions folder...")
        try:
            subprocess.check_call(["cp", "Versions/oauth_auth.py", "."])
            print("oauth_auth.py copied successfully.")
        except subprocess.CalledProcessError:
            print("Error copying oauth_auth.py. Please copy it manually.")

if __name__ == "__main__":
    print("Databricks Genie Web Application Launcher")
    print("=========================================")

    # Copy version folders if they don't exist
    copy_version_folders()

    # Check if dependencies are installed
    if not check_dependencies():
        print("Required packages are not installed.")
        install_dependencies()

    # Show the menu
    show_menu()
