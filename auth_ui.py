#!/usr/bin/env python3
"""
Databricks Genie Authentication UI

This script provides a simple UI for managing authentication settings
for the Databricks Genie API tools.

Usage:
    python auth_ui.py
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from dotenv import load_dotenv, set_key, find_dotenv

# Load environment variables
dotenv_path = find_dotenv()
if not dotenv_path:
    dotenv_path = '.env'
load_dotenv(dotenv_path)

class GenieAuthUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Databricks Genie Authentication")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # Create main frame
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create title
        title_label = ttk.Label(main_frame, text="Databricks Genie Authentication", font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 20))
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Create tabs
        self.create_connection_tab()
        self.create_pat_tab()
        self.create_oauth_tab()
        
        # Create buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=(20, 0))
        
        # Create save button
        save_button = ttk.Button(buttons_frame, text="Save Settings", command=self.save_settings)
        save_button.pack(side=tk.RIGHT, padx=5)
        
        # Create test button
        test_button = ttk.Button(buttons_frame, text="Test Connection", command=self.test_connection)
        test_button.pack(side=tk.RIGHT, padx=5)
        
        # Load existing settings
        self.load_settings()
    
    def create_connection_tab(self):
        """Create the connection settings tab"""
        tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(tab, text="Connection")
        
        # Databricks Host
        ttk.Label(tab, text="Databricks Host:").grid(column=0, row=0, sticky=tk.W, pady=5)
        self.host_var = tk.StringVar()
        host_entry = ttk.Entry(tab, textvariable=self.host_var, width=50)
        host_entry.grid(column=1, row=0, sticky=(tk.W, tk.E), pady=5)
        ttk.Label(tab, text="Example: dbc-123abc45-6def.cloud.databricks.com").grid(column=1, row=1, sticky=tk.W, pady=(0, 10))
        
        # Genie Space ID
        ttk.Label(tab, text="Genie Space ID:").grid(column=0, row=2, sticky=tk.W, pady=5)
        self.space_id_var = tk.StringVar()
        space_id_entry = ttk.Entry(tab, textvariable=self.space_id_var, width=50)
        space_id_entry.grid(column=1, row=2, sticky=(tk.W, tk.E), pady=5)
        ttk.Label(tab, text="Example: your-space-id?o=your-org-id").grid(column=1, row=3, sticky=tk.W, pady=(0, 10))
        
        # Authentication Method
        ttk.Label(tab, text="Authentication Method:").grid(column=0, row=4, sticky=tk.W, pady=5)
        self.auth_method_var = tk.StringVar(value="PAT")
        auth_method_frame = ttk.Frame(tab)
        auth_method_frame.grid(column=1, row=4, sticky=tk.W, pady=5)
        
        ttk.Radiobutton(auth_method_frame, text="Personal Access Token (PAT)", 
                        variable=self.auth_method_var, value="PAT").pack(anchor=tk.W)
        ttk.Radiobutton(auth_method_frame, text="OAuth (Service Principal)", 
                        variable=self.auth_method_var, value="OAuth").pack(anchor=tk.W)
    
    def create_pat_tab(self):
        """Create the PAT authentication tab"""
        tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(tab, text="PAT Authentication")
        
        # PAT Token
        ttk.Label(tab, text="Personal Access Token:").grid(column=0, row=0, sticky=tk.W, pady=5)
        self.token_var = tk.StringVar()
        token_entry = ttk.Entry(tab, textvariable=self.token_var, width=50, show="*")
        token_entry.grid(column=1, row=0, sticky=(tk.W, tk.E), pady=5)
        
        # Show/Hide token
        self.show_token_var = tk.BooleanVar(value=False)
        show_token_check = ttk.Checkbutton(tab, text="Show token", 
                                          variable=self.show_token_var, 
                                          command=lambda: token_entry.config(show="" if self.show_token_var.get() else "*"))
        show_token_check.grid(column=1, row=1, sticky=tk.W, pady=5)
        
        # Help text
        help_frame = ttk.LabelFrame(tab, text="Help", padding=10)
        help_frame.grid(column=0, row=2, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        help_text = ("To generate a Personal Access Token:\n"
                    "1. Log in to your Databricks workspace\n"
                    "2. Click your username in the top-right corner\n"
                    "3. Select 'User Settings'\n"
                    "4. Go to the 'Access Tokens' tab\n"
                    "5. Click 'Generate New Token'\n"
                    "6. Provide a name and expiration, then click 'Generate'\n"
                    "7. Copy the token and paste it here")
        
        ttk.Label(help_frame, text=help_text, justify=tk.LEFT).pack(anchor=tk.W)
    
    def create_oauth_tab(self):
        """Create the OAuth authentication tab"""
        tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(tab, text="OAuth Authentication")
        
        # Client ID
        ttk.Label(tab, text="Client ID:").grid(column=0, row=0, sticky=tk.W, pady=5)
        self.client_id_var = tk.StringVar()
        ttk.Entry(tab, textvariable=self.client_id_var, width=50).grid(column=1, row=0, sticky=(tk.W, tk.E), pady=5)
        
        # Client Secret
        ttk.Label(tab, text="Client Secret:").grid(column=0, row=1, sticky=tk.W, pady=5)
        self.client_secret_var = tk.StringVar()
        client_secret_entry = ttk.Entry(tab, textvariable=self.client_secret_var, width=50, show="*")
        client_secret_entry.grid(column=1, row=1, sticky=(tk.W, tk.E), pady=5)
        
        # Show/Hide secret
        self.show_secret_var = tk.BooleanVar(value=False)
        show_secret_check = ttk.Checkbutton(tab, text="Show secret", 
                                           variable=self.show_secret_var, 
                                           command=lambda: client_secret_entry.config(show="" if self.show_secret_var.get() else "*"))
        show_secret_check.grid(column=1, row=2, sticky=tk.W, pady=5)
        
        # Account ID (optional)
        ttk.Label(tab, text="Account ID (optional):").grid(column=0, row=3, sticky=tk.W, pady=5)
        self.account_id_var = tk.StringVar()
        ttk.Entry(tab, textvariable=self.account_id_var, width=50).grid(column=1, row=3, sticky=(tk.W, tk.E), pady=5)
        ttk.Label(tab, text="Only needed for account-level operations").grid(column=1, row=4, sticky=tk.W, pady=(0, 10))
        
        # Help text
        help_frame = ttk.LabelFrame(tab, text="Help", padding=10)
        help_frame.grid(column=0, row=5, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        help_text = ("To set up OAuth authentication:\n"
                    "1. Create a service principal in your Databricks workspace\n"
                    "2. Generate an OAuth secret for the service principal\n"
                    "3. Grant the service principal access to your Genie spaces\n"
                    "4. Enter the Client ID and Client Secret here\n\n"
                    "For detailed instructions, see OAUTH_README.md")
        
        ttk.Label(help_frame, text=help_text, justify=tk.LEFT).pack(anchor=tk.W)
        
        # View OAuth README button
        ttk.Button(tab, text="View OAuth README", command=self.view_oauth_readme).grid(column=1, row=6, sticky=tk.E, pady=10)
    
    def load_settings(self):
        """Load existing settings from .env file"""
        # Connection settings
        self.host_var.set(os.getenv("DATABRICKS_HOST", ""))
        self.space_id_var.set(os.getenv("DATABRICKS_SPACE_ID", ""))
        
        # Determine auth method
        if os.getenv("DATABRICKS_CLIENT_ID") and os.getenv("DATABRICKS_CLIENT_SECRET"):
            self.auth_method_var.set("OAuth")
        else:
            self.auth_method_var.set("PAT")
        
        # PAT settings
        self.token_var.set(os.getenv("DATABRICKS_TOKEN", ""))
        
        # OAuth settings
        self.client_id_var.set(os.getenv("DATABRICKS_CLIENT_ID", ""))
        self.client_secret_var.set(os.getenv("DATABRICKS_CLIENT_SECRET", ""))
        self.account_id_var.set(os.getenv("DATABRICKS_ACCOUNT_ID", ""))
    
    def save_settings(self):
        """Save settings to .env file"""
        # Validate required fields
        if not self.host_var.get():
            messagebox.showerror("Error", "Databricks Host is required")
            return
        
        if not self.space_id_var.get():
            messagebox.showerror("Error", "Genie Space ID is required")
            return
        
        if self.auth_method_var.get() == "PAT" and not self.token_var.get():
            messagebox.showerror("Error", "Personal Access Token is required")
            return
        
        if self.auth_method_var.get() == "OAuth" and (not self.client_id_var.get() or not self.client_secret_var.get()):
            messagebox.showerror("Error", "Client ID and Client Secret are required for OAuth authentication")
            return
        
        # Update .env file
        env_vars = {
            "DATABRICKS_HOST": self.host_var.get(),
            "DATABRICKS_SPACE_ID": self.space_id_var.get(),
        }
        
        # Add auth-specific variables
        if self.auth_method_var.get() == "PAT":
            env_vars["DATABRICKS_TOKEN"] = self.token_var.get()
            # Clear OAuth variables if switching to PAT
            env_vars["DATABRICKS_CLIENT_ID"] = ""
            env_vars["DATABRICKS_CLIENT_SECRET"] = ""
            env_vars["DATABRICKS_ACCOUNT_ID"] = ""
        else:  # OAuth
            env_vars["DATABRICKS_CLIENT_ID"] = self.client_id_var.get()
            env_vars["DATABRICKS_CLIENT_SECRET"] = self.client_secret_var.get()
            env_vars["DATABRICKS_ACCOUNT_ID"] = self.account_id_var.get()
            # Clear PAT if switching to OAuth
            env_vars["DATABRICKS_TOKEN"] = ""
        
        # Write to .env file
        for key, value in env_vars.items():
            set_key(dotenv_path, key, value)
        
        messagebox.showinfo("Success", "Settings saved successfully")
    
    def test_connection(self):
        """Test the connection to Databricks Genie API"""
        # Save settings first
        self.save_settings()
        
        # Import necessary modules
        try:
            import requests
            if self.auth_method_var.get() == "OAuth":
                try:
                    from oauth_auth import get_oauth_token, get_auth_headers
                    headers = get_auth_headers()
                except ImportError:
                    messagebox.showerror("Error", "OAuth authentication module not found")
                    return
            else:  # PAT
                headers = {
                    "Authorization": f"Bearer {self.token_var.get()}",
                    "Content-Type": "application/json"
                }
            
            # Test connection
            host = self.host_var.get()
            space_id = self.space_id_var.get().split('?')[0]  # Remove query parameters
            
            # First try a simple API call to check token validity
            url = f"https://{host}/api/2.0/clusters/list"
            
            response = requests.get(url, headers=headers)
            
            if response.status_code == 200:
                # Now try the Genie API
                genie_url = f"https://{host}/api/2.0/genie/spaces/{space_id}"
                genie_response = requests.get(genie_url, headers=headers)
                
                if genie_response.status_code == 200:
                    messagebox.showinfo("Success", "Connection successful! Both Databricks API and Genie API are accessible.")
                else:
                    messagebox.showwarning("Partial Success", 
                                          f"Databricks API connection successful, but Genie API returned status code {genie_response.status_code}.\n\n"
                                          f"This may be due to permission issues. If using OAuth, ensure the service principal has access to Genie spaces.")
            else:
                messagebox.showerror("Error", f"Connection failed with status code {response.status_code}.\n\n{response.text}")
        
        except Exception as e:
            messagebox.showerror("Error", f"Connection test failed: {str(e)}")
    
    def view_oauth_readme(self):
        """Open the OAuth README file"""
        try:
            if sys.platform == 'win32':
                os.startfile('OAUTH_README.md')
            elif sys.platform == 'darwin':  # macOS
                os.system('open OAUTH_README.md')
            else:  # Linux
                os.system('xdg-open OAUTH_README.md')
        except Exception as e:
            messagebox.showerror("Error", f"Could not open OAUTH_README.md: {str(e)}")

def main():
    """Main function"""
    root = tk.Tk()
    app = GenieAuthUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()