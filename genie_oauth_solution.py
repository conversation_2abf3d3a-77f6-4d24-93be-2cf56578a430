#!/usr/bin/env python3
"""
Databricks Genie API OAuth Authentication Solution

This script provides a complete solution for using OAuth authentication with the Databricks Genie API.
It includes functions for:
- Getting an OAuth token
- Accessing Genie spaces
- Starting conversations
- Retrieving and processing messages

Usage:
    python genie_oauth_solution.py
"""

import os
import json
import time
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")

# Check if required environment variables are set
if not all([DATABRICKS_HOST, DATABRICKS_CLIENT_ID, DATAB<PERSON><PERSON>KS_CLIENT_SECRET, DATABRI<PERSON>KS_SPACE_ID]):
    print("Error: Missing required environment variables.")
    print("Please set DATAB<PERSON>CKS_HOST, DATABRICKS_CLIENT_ID, DATABRICKS_CLIENT_SECRET, and DATABRICKS_SPACE_ID in your .env file.")
    exit(1)

# Extract the space ID and org ID
space_id_full = DATABRICKS_SPACE_ID
space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full
org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

# Remove any 'datarooms/' prefix if it exists
if space_id.startswith('datarooms/'):
    space_id = space_id.replace('datarooms/', '')

# Token cache for OAuth
_token_cache = {
    "access_token": None,
    "expires_at": 0
}

def get_oauth_token():
    """
    Get an OAuth token for authenticating with the Databricks API.

    Returns:
        str: The OAuth access token
    """
    global _token_cache

    # Check if we have a valid cached token
    current_time = time.time()
    if _token_cache["access_token"] and _token_cache["expires_at"] > current_time + 60:
        return _token_cache["access_token"]

    # No valid token, request a new one
    # Workspace-level token endpoint
    token_endpoint = f"https://{DATABRICKS_HOST}/oidc/v1/token"

    # Request the token
    response = requests.post(
        token_endpoint,
        auth=(DATABRICKS_CLIENT_ID, DATABRICKS_CLIENT_SECRET),
        data={"grant_type": "client_credentials", "scope": "all-apis"}
    )

    # Check if the request was successful
    if response.status_code != 200:
        raise Exception(f"Failed to get OAuth token: {response.text}")

    # Cache the token
    token_data = response.json()
    _token_cache["access_token"] = token_data["access_token"]
    _token_cache["expires_at"] = current_time + token_data["expires_in"]

    return _token_cache["access_token"]

def get_oauth_headers():
    """
    Get headers for OAuth authentication.

    Returns:
        dict: The headers to use for API requests
    """
    token = get_oauth_token()
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def get_genie_space(space_id=None):
    """
    Get information about a specific Genie space using OAuth authentication.

    Args:
        space_id (str, optional): The ID of the Genie space. Defaults to the space ID from the .env file.

    Returns:
        dict: The Genie space information
    """
    if not space_id:
        space_id = globals()["space_id"]

    # Get OAuth headers
    headers = get_oauth_headers()

    # Use the correct URL format for accessing a specific Genie space
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}"

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to get Genie space: {response.text}")

def start_conversation(question, space_id=None):
    """
    Start a new conversation in a Genie space using OAuth authentication.

    Args:
        question (str): The question to ask
        space_id (str, optional): The ID of the Genie space. Defaults to the space ID from the .env file.

    Returns:
        dict: The conversation data including conversation_id and message_id
    """
    if not space_id:
        space_id = globals()["space_id"]

    # Get OAuth headers
    headers = get_oauth_headers()

    # Use the correct URL format for starting a conversation
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation"

    # Prepare the payload
    payload = {"content": question}

    response = requests.post(url, headers=headers, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to start conversation: {response.text}")

def get_message(conversation_id, message_id, space_id=None):
    """
    Get a message from a conversation using OAuth authentication.

    Args:
        conversation_id (str): The ID of the conversation
        message_id (str): The ID of the message
        space_id (str, optional): The ID of the Genie space. Defaults to the space ID from the .env file.

    Returns:
        dict: The message data
    """
    if not space_id:
        space_id = globals()["space_id"]

    # Get OAuth headers
    headers = get_oauth_headers()

    # Use the correct URL format for getting a message
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}"

    response = requests.get(url, headers=headers)

    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Failed to get message: {response.text}")

def wait_for_message_completion(conversation_id, message_id, space_id=None, max_wait_seconds=120, poll_interval=2):
    """
    Wait for a message to complete processing.

    Args:
        conversation_id (str): The ID of the conversation
        message_id (str): The ID of the message
        space_id (str, optional): The ID of the Genie space. Defaults to the space ID from the .env file.
        max_wait_seconds (int, optional): Maximum time to wait in seconds. Defaults to 120.
        poll_interval (int, optional): Time between status checks in seconds. Defaults to 2.

    Returns:
        dict: The completed message data
    """
    if not space_id:
        space_id = globals()["space_id"]

    start_time = time.time()
    elapsed_time = 0

    while elapsed_time < max_wait_seconds:
        # Get the message
        message_data = get_message(conversation_id, message_id, space_id)

        # Check if the message is complete
        status = message_data.get("status")

        if status == "COMPLETED":
            return message_data
        elif status in ["SUBMITTED", "PROCESSING", "ASKING_AI", "PENDING_WAREHOUSE"]:
            # Message is still processing, wait and try again
            time.sleep(poll_interval)
            elapsed_time = time.time() - start_time
        else:
            # Unknown status, continue waiting
            time.sleep(poll_interval)
            elapsed_time = time.time() - start_time

    # Timed out, return the last message data
    return message_data

def extract_response_content(message_data):
    """
    Extract the response content from a message.

    Args:
        message_data (dict): The message data

    Returns:
        dict: A dictionary containing the response content and additional data
    """
    result = {
        "text": None,
        "sql": None,
        "sql_results": None,
        "visualization": None,
        "attachments": [],
        "raw_data": message_data
    }

    # Check if the message is complete
    if message_data.get("status") != "COMPLETED":
        return result

    # Extract the response object
    response_obj = message_data.get("response", {})
    if not isinstance(response_obj, dict):
        response_obj = {}

    # Extract the text content
    result["text"] = response_obj.get("content")

    # Extract the SQL query from response
    if "sql" in response_obj:
        result["sql"] = response_obj.get("sql")

    # Extract the SQL results from response
    if "sql_results" in response_obj and isinstance(response_obj["sql_results"], list):
        result["sql_results"] = response_obj["sql_results"]

    # Extract visualization data
    if "visualization" in response_obj:
        result["visualization"] = response_obj.get("visualization")

    # Extract attachments (this is where the SQL query is often stored)
    if "attachments" in message_data and isinstance(message_data["attachments"], list):
        result["attachments"] = message_data["attachments"]

        # Extract SQL query from attachments if not already found
        if not result["sql"]:
            for attachment in message_data["attachments"]:
                if "query" in attachment and isinstance(attachment["query"], dict):
                    query_obj = attachment["query"]
                    if "query" in query_obj:
                        result["sql"] = query_obj["query"]
                    if "description" in query_obj and not result["text"]:
                        # Use the query description as text if no other text is available
                        result["text"] = query_obj["description"]

    # If we couldn't find the response content in the expected places, try other locations
    if not result["text"]:
        # Try to get it from the content field
        if "content" in message_data:
            result["text"] = message_data["content"]

        # Try to get it from the message field
        elif "message" in message_data and isinstance(message_data["message"], dict):
            result["text"] = message_data["message"].get("content")

    # If we still don't have text content but have SQL results, format them as text
    if not result["text"] and result["sql_results"]:
        result["text"] = "SQL Results:\n"
        for sql_result in result["sql_results"]:
            result["text"] += json.dumps(sql_result, indent=2) + "\n"

    # If we still don't have text content but have SQL query, use that
    if not result["text"] and result["sql"]:
        result["text"] = "The query was executed but returned no results. Here's the SQL query that was used:\n\n" + result["sql"]

    return result

def ask_genie(question, space_id=None, max_wait_seconds=120):
    """
    Ask a question to Genie and get the response.

    Args:
        question (str): The question to ask
        space_id (str, optional): The ID of the Genie space. Defaults to the space ID from the .env file.
        max_wait_seconds (int, optional): Maximum time to wait for a response in seconds. Defaults to 120.

    Returns:
        dict: A dictionary containing the response content and additional data
    """
    if not space_id:
        space_id = globals()["space_id"]

    # Start a conversation
    conversation_data = start_conversation(question, space_id)

    # Extract conversation ID and message ID
    conversation_id = conversation_data.get("conversation_id")
    message_id = conversation_data.get("message_id")

    if not conversation_id or not message_id:
        raise Exception("Failed to get conversation ID or message ID")

    # Wait for the message to complete
    completed_message = wait_for_message_completion(conversation_id, message_id, space_id, max_wait_seconds)

    # Extract and return the response
    response_data = extract_response_content(completed_message)

    if not response_data["text"] and not response_data["sql_results"]:
        # If we still don't have any content, include the raw data for debugging
        print("Warning: No response content found. Raw response data:")
        print(json.dumps(completed_message, indent=2))

    return response_data

def main():
    """Main function to demonstrate the OAuth authentication with Genie API"""
    print("=== Databricks Genie API OAuth Authentication Solution ===")
    print(f"Host: {DATABRICKS_HOST}")
    print(f"Space ID: {space_id}")
    print(f"Organization ID: {org_id}")

    try:
        # Get OAuth token
        token = get_oauth_token()
        print(f"\n✅ Successfully obtained OAuth token: {token[:10]}...")

        # Get Genie space information
        space_data = get_genie_space()
        print(f"\n✅ Successfully accessed Genie space: {space_data.get('title')}")

        # Ask a question to Genie
        question = "List the first 5 rows from the sales_transactions table in the samples.bakehouse schema"
        print(f"\n🤔 Asking Genie: '{question}'")
        print("Please wait, this may take a minute...")

        response_data = ask_genie(question)

        print("\n✅ Genie Response:")

        # Display the text response if available
        if response_data["text"]:
            print("\nText Response:")
            print(response_data["text"])

        # Display the SQL query if available
        if response_data["sql"]:
            print("\nSQL Query:")
            print(response_data["sql"])

        # Display the SQL results if available
        if response_data["sql_results"]:
            print("\nSQL Results:")
            for i, result in enumerate(response_data["sql_results"]):
                print(f"\nResult {i+1}:")
                print(json.dumps(result, indent=2))

        # Display attachments if available
        if response_data["attachments"]:
            print("\nAttachments:")
            for i, attachment in enumerate(response_data["attachments"]):
                print(f"\nAttachment {i+1}:")
                if "query" in attachment and isinstance(attachment["query"], dict):
                    query_obj = attachment["query"]
                    if "description" in query_obj:
                        print(f"Description: {query_obj['description']}")
                    if "query_result_metadata" in query_obj:
                        print(f"Query Result Metadata: {json.dumps(query_obj['query_result_metadata'], indent=2)}")

        # Mention visualization if available
        if response_data["visualization"]:
            print("\nVisualization data is available in the response.")

        # Print the raw response data for debugging
        print("\nDEBUG - Raw Response Data:")
        print(json.dumps(response_data["raw_data"], indent=2))

        print("\n🎉 OAuth authentication with Genie API is working correctly!")
    except Exception as e:
        print(f"\n❌ Error: {str(e)}")

if __name__ == "__main__":
    main()
