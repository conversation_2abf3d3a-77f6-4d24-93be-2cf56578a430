#!/usr/bin/env python3
"""
Fix Databricks Genie API OAuth Permissions

This script provides a step-by-step guide to fix the 403 "PERMISSION_DENIED" error
when accessing the Databricks Genie API with OAuth authentication.

The error "You need 'Can View' permission to perform this action" occurs because
the service principal used for OAuth authentication doesn't have the necessary
permissions to access the Genie space.

Usage:
    python fix_genie_oauth_permissions.py
"""

import os
import json
import requests
import webbrowser
from dotenv import load_dotenv

# Import OAuth authentication module
try:
    from oauth_auth import get_auth_headers, get_oauth_token
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")  # PAT for admin operations
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")

# Check if required environment variables are set
if not DATABRICKS_HOST or not DATABRICKS_SPACE_ID:
    print("Error: Missing required environment variables.")
    print("Please set DATABRICKS_HOST and DATABRICKS_SPACE_ID in your .env file.")
    exit(1)

if not OAUTH_AVAILABLE or not DATABRICKS_CLIENT_ID or not DATABRICKS_CLIENT_SECRET:
    print("Error: OAuth authentication not available.")
    print("Please set DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET in your .env file.")
    exit(1)

# Extract the space ID and org ID
space_id_full = DATABRICKS_SPACE_ID
space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full
org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

def get_pat_headers():
    """Get headers for PAT authentication"""
    return {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }

def get_service_principal_info():
    """Get information about the service principal"""
    print("\n=== Getting Service Principal Information ===")
    
    if not DATABRICKS_TOKEN:
        print("❌ Cannot get service principal info without a PAT token.")
        return None
    
    headers = get_pat_headers()
    url = f"https://{DATABRICKS_HOST}/api/2.0/preview/scim/v2/ServicePrincipals"
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            for sp in data.get("Resources", []):
                if sp.get("applicationId") == DATABRICKS_CLIENT_ID:
                    print(f"✅ Found service principal: {sp.get('displayName')}")
                    print(f"   ID: {sp.get('id')}")
                    print(f"   Application ID: {sp.get('applicationId')}")
                    return sp
            
            print("❌ Service principal not found with the given client ID.")
            return None
        else:
            print(f"❌ Failed to get service principals (Status: {response.status_code})")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error getting service principal info: {str(e)}")
        return None

def get_all_genie_spaces():
    """Get all Genie spaces in the workspace"""
    print("\n=== Getting All Genie Spaces ===")
    
    if not DATABRICKS_TOKEN:
        print("❌ Cannot get Genie spaces without a PAT token.")
        return []
    
    headers = get_pat_headers()
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms"
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            spaces = data.get("spaces", [])
            print(f"✅ Found {len(spaces)} Genie spaces")
            for i, space in enumerate(spaces[:5]):  # Show up to 5 spaces
                print(f"   {i+1}. {space.get('name', 'Unnamed')} (ID: {space.get('id', 'Unknown')})")
            if len(spaces) > 5:
                print(f"   ... and {len(spaces) - 5} more")
            return spaces
        else:
            print(f"❌ Failed to get Genie spaces (Status: {response.status_code})")
            print(f"Response: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Error getting Genie spaces: {str(e)}")
        return []

def test_oauth_genie_access():
    """Test OAuth access to the Genie API"""
    print("\n=== Testing OAuth Access to Genie API ===")
    
    # Get OAuth headers
    headers = get_auth_headers()
    
    # Test with the correct URL format for the Genie API
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms"
    
    print(f"Testing URL: {url}")
    
    try:
        response = requests.get(url, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ OAuth authentication to Genie API successful!")
            data = response.json()
            spaces = data.get("spaces", [])
            print(f"Found {len(spaces)} Genie spaces")
            return True
        else:
            print("❌ OAuth authentication to Genie API failed")
            print(f"Response: {response.text}")
            
            if response.status_code == 403 and "PERMISSION_DENIED" in response.text:
                print("\n⚠️ Permission denied error detected.")
                print("The service principal needs 'Can View' permission on Genie spaces.")
            
            return False
    except Exception as e:
        print(f"❌ Error testing OAuth access: {str(e)}")
        return False

def provide_permission_instructions():
    """Provide instructions on how to fix the permissions"""
    print("\n=== How to Fix the Permissions ===")
    
    # Get service principal info
    sp_info = get_service_principal_info()
    spaces = get_all_genie_spaces()
    
    print("\nTo fix the 403 'PERMISSION_DENIED' error, you need to grant 'Can View' permission")
    print("to your service principal for the Genie spaces you want to access.")
    
    if sp_info:
        sp_name = sp_info.get('displayName')
        sp_id = sp_info.get('id')
        
        print("\nFollow these steps:")
        print(f"1. Log in to your Databricks workspace: https://{DATABRICKS_HOST}")
        print("2. Navigate to the Genie section in the sidebar")
        
        if spaces:
            print("3. For each Genie space you want to access:")
            for i, space in enumerate(spaces[:5]):
                print(f"   a. Open the space named '{space.get('name', 'Unnamed')}'")
                print("   b. Click the 'Share' button in the top-right corner")
                print(f"   c. Search for the service principal named '{sp_name}' (ID: {sp_id})")
                print("   d. Grant at least 'Can View' permission to the service principal")
                print("   e. Click 'Save' to apply the permissions")
        else:
            print("3. For each Genie space you want to access:")
            print("   a. Open the space")
            print("   b. Click the 'Share' button in the top-right corner")
            print(f"   c. Search for the service principal named '{sp_name}' (ID: {sp_id})")
            print("   d. Grant at least 'Can View' permission to the service principal")
            print("   e. Click 'Save' to apply the permissions")
    else:
        print("\nFollow these steps:")
        print(f"1. Log in to your Databricks workspace: https://{DATABRICKS_HOST}")
        print("2. Navigate to the Genie section in the sidebar")
        print("3. For each Genie space you want to access:")
        print("   a. Open the space")
        print("   b. Click the 'Share' button in the top-right corner")
        print("   c. Search for your service principal (the one with client ID matching your OAuth credentials)")
        print("   d. Grant at least 'Can View' permission to the service principal")
        print("   e. Click 'Save' to apply the permissions")
    
    print("\nAdditional steps for workspace administrators:")
    print("1. Go to the Databricks workspace settings")
    print("2. Navigate to the 'Admin Console' section")
    print("3. Go to the 'Users and Groups' tab")
    print("4. Find your service principal")
    print("5. Ensure it has the necessary permissions to access Genie spaces")
    print("6. You may need to add the service principal to a group with appropriate permissions")
    
    # Open the workspace in a browser
    workspace_url = f"https://{DATABRICKS_HOST}/genie"
    print(f"\nOpening workspace in browser: {workspace_url}")
    try:
        webbrowser.open(workspace_url)
    except:
        print("Could not open browser automatically. Please open the URL manually.")

def main():
    """Main function"""
    print("=== Databricks Genie API OAuth Permission Fixer ===")
    print(f"Host: {DATABRICKS_HOST}")
    print(f"Space ID: {space_id}")
    print(f"Organization ID: {org_id}")
    
    # Test OAuth access to Genie API
    if test_oauth_genie_access():
        print("\n✅ OAuth authentication to Genie API is working correctly!")
        print("You have the necessary permissions to access Genie spaces.")
        return
    
    # Provide permission instructions
    provide_permission_instructions()
    
    print("\nAfter granting permissions, run this script again to verify the fix.")

if __name__ == "__main__":
    main()
