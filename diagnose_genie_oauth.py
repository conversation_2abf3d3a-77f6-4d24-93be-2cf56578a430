#!/usr/bin/env python3
"""
Diagnose Databricks Genie API OAuth Authentication Issues

This script performs detailed diagnostics on OAuth authentication with the Databricks Genie API
to identify the specific cause of the 403 permission denied error.
"""

import os
import json
import requests
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")  # PAT for comparison
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")
CURRENT_AUTH_TYPE = os.getenv("CURRENT_AUTH_TYPE", "oauth")

# Extract the space ID and org ID
space_id_full = DATABRICKS_SPACE_ID
space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full
org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

# Remove any 'datarooms/' prefix if it exists
if space_id.startswith('datarooms/'):
    space_id = space_id.replace('datarooms/', '')

# Token cache for OAuth
_token_cache = {
    "access_token": None,
    "expires_at": 0
}

def get_oauth_token():
    """Get an OAuth token for authenticating with the Databricks API"""
    global _token_cache

    # Check if we have a valid cached token
    current_time = time.time()
    if _token_cache["access_token"] and _token_cache["expires_at"] > current_time + 60:
        return _token_cache["access_token"]

    # No valid token, request a new one
    if DATABRICKS_HOST and DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET:
        # Workspace-level token endpoint
        token_endpoint = f"https://{DATABRICKS_HOST}/oidc/v1/token"

        # Request the token
        response = requests.post(
            token_endpoint,
            auth=(DATABRICKS_CLIENT_ID, DATABRICKS_CLIENT_SECRET),
            data={"grant_type": "client_credentials", "scope": "all-apis"}
        )

        # Check if the request was successful
        if response.status_code != 200:
            raise Exception(f"Failed to get OAuth token: {response.text}")

        # Cache the token
        token_data = response.json()
        _token_cache["access_token"] = token_data["access_token"]
        _token_cache["expires_at"] = current_time + token_data["expires_in"]

        return _token_cache["access_token"]
    else:
        raise ValueError("Missing required OAuth credentials")

def get_oauth_headers():
    """Get headers for OAuth authentication"""
    token = get_oauth_token()
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def get_pat_headers():
    """Get headers for PAT authentication"""
    return {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }

def test_api_endpoint(name, url, method="GET", headers=None, data=None, expected_status=200):
    """Test an API endpoint and return the result"""
    print(f"\n=== Testing {name} ===")
    print(f"{method} {url}")
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=headers)
        elif method.upper() == "POST":
            response = requests.post(url, headers=headers, json=data)
        else:
            print(f"Unsupported method: {method}")
            return False, None
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == expected_status:
            print(f"✅ Success! Expected status code {expected_status}")
            try:
                return True, response.json()
            except:
                return True, response.text
        else:
            print(f"❌ Failed! Expected status code {expected_status}, got {response.status_code}")
            print(f"Response: {response.text}")
            return False, response.text
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False, str(e)

def compare_pat_and_oauth():
    """Compare PAT and OAuth authentication for various endpoints"""
    results = {}
    
    # Skip if PAT is not available
    if not DATABRICKS_TOKEN:
        print("PAT token not available, skipping comparison")
        return results
    
    # Get headers for both authentication methods
    pat_headers = get_pat_headers()
    try:
        oauth_headers = get_oauth_headers()
    except Exception as e:
        print(f"❌ Failed to get OAuth headers: {str(e)}")
        return results
    
    # Test endpoints with both authentication methods
    endpoints = [
        {
            "name": "Clusters List",
            "url": f"https://{DATABRICKS_HOST}/api/2.0/clusters/list",
            "method": "GET"
        },
        {
            "name": "Genie Spaces List",
            "url": f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms",
            "method": "GET"
        },
        {
            "name": "Specific Genie Space",
            "url": f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}",
            "method": "GET"
        },
        {
            "name": "Start Conversation",
            "url": f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}/start-conversation?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}/start-conversation",
            "method": "POST",
            "data": {"content": "Show me the top 10 customers by revenue"}
        }
    ]
    
    print("\n=== Comparing PAT and OAuth Authentication ===")
    
    for endpoint in endpoints:
        name = endpoint["name"]
        url = endpoint["url"]
        method = endpoint.get("method", "GET")
        data = endpoint.get("data")
        
        print(f"\n--- Testing {name} ---")
        
        # Test with PAT
        print("\nUsing PAT Authentication:")
        pat_success, pat_response = test_api_endpoint(
            f"{name} (PAT)", url, method, pat_headers, data
        )
        
        # Test with OAuth
        print("\nUsing OAuth Authentication:")
        oauth_success, oauth_response = test_api_endpoint(
            f"{name} (OAuth)", url, method, oauth_headers, data
        )
        
        results[name] = {
            "pat": {"success": pat_success, "response": pat_response},
            "oauth": {"success": oauth_success, "response": oauth_response}
        }
    
    return results

def check_service_principal_permissions():
    """Check permissions of the service principal"""
    print("\n=== Checking Service Principal Permissions ===")
    
    if not DATABRICKS_TOKEN:
        print("❌ PAT token not available, cannot check service principal permissions")
        return None
    
    headers = get_pat_headers()
    
    # Get service principal info
    url = f"https://{DATABRICKS_HOST}/api/2.0/preview/scim/v2/ServicePrincipals"
    success, response = test_api_endpoint("Service Principals List", url, headers=headers)
    
    if not success:
        print("❌ Failed to get service principals list")
        return None
    
    # Find the service principal with the matching client ID
    service_principal = None
    for sp in response.get("Resources", []):
        if sp.get("applicationId") == DATABRICKS_CLIENT_ID:
            service_principal = sp
            break
    
    if not service_principal:
        print(f"❌ Service principal with client ID {DATABRICKS_CLIENT_ID} not found")
        return None
    
    print(f"✅ Found service principal: {service_principal.get('displayName')}")
    print(f"   ID: {service_principal.get('id')}")
    print(f"   Application ID: {service_principal.get('applicationId')}")
    
    # Check group memberships
    groups_url = f"https://{DATABRICKS_HOST}/api/2.0/preview/scim/v2/Groups"
    success, groups_response = test_api_endpoint("Groups List", groups_url, headers=headers)
    
    if not success:
        print("❌ Failed to get groups list")
        return service_principal
    
    sp_id = service_principal.get('id')
    sp_groups = []
    
    for group in groups_response.get("Resources", []):
        members = group.get("members", [])
        for member in members:
            if member.get("value") == sp_id:
                sp_groups.append(group.get("displayName"))
                break
    
    if sp_groups:
        print(f"✅ Service principal is a member of the following groups:")
        for group in sp_groups:
            print(f"   - {group}")
    else:
        print("⚠️ Service principal is not a member of any groups")
    
    return service_principal

def check_genie_space_permissions():
    """Check permissions on the Genie space"""
    print("\n=== Checking Genie Space Permissions ===")
    
    if not DATABRICKS_TOKEN:
        print("❌ PAT token not available, cannot check Genie space permissions")
        return None
    
    headers = get_pat_headers()
    
    # Get Genie space info
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}"
    success, response = test_api_endpoint("Genie Space Info", url, headers=headers)
    
    if not success:
        print("❌ Failed to get Genie space info")
        return None
    
    print(f"✅ Found Genie space: {response.get('name')}")
    print(f"   ID: {response.get('id')}")
    
    # Unfortunately, there's no direct API to check permissions on a Genie space
    # We can only infer from the ability to access it
    
    return response

def main():
    """Main function"""
    print("=== Databricks Genie API OAuth Authentication Diagnostics ===")
    print(f"Host: {DATABRICKS_HOST}")
    print(f"Space ID: {space_id}")
    print(f"Organization ID: {org_id}")
    print(f"Current Auth Type: {CURRENT_AUTH_TYPE}")
    
    # Check service principal permissions
    service_principal = check_service_principal_permissions()
    
    # Check Genie space permissions
    genie_space = check_genie_space_permissions()
    
    # Compare PAT and OAuth authentication
    comparison_results = compare_pat_and_oauth()
    
    # Analyze results and provide recommendations
    print("\n=== Analysis and Recommendations ===")
    
    # Check if OAuth token can be obtained
    try:
        oauth_token = get_oauth_token()
        print("✅ OAuth token can be obtained successfully")
    except Exception as e:
        print(f"❌ Failed to get OAuth token: {str(e)}")
        print("\nRecommendation: Verify your OAuth credentials (client ID and client secret)")
        return
    
    # Check if standard API calls work with OAuth
    if comparison_results.get("Clusters List", {}).get("oauth", {}).get("success", False):
        print("✅ OAuth authentication works for standard API calls")
    else:
        print("❌ OAuth authentication fails for standard API calls")
        print("\nRecommendation: Verify your OAuth credentials and service principal permissions")
        return
    
    # Check if Genie API calls work with OAuth
    genie_spaces_oauth_success = comparison_results.get("Genie Spaces List", {}).get("oauth", {}).get("success", False)
    genie_space_oauth_success = comparison_results.get("Specific Genie Space", {}).get("oauth", {}).get("success", False)
    start_conversation_oauth_success = comparison_results.get("Start Conversation", {}).get("oauth", {}).get("success", False)
    
    if genie_spaces_oauth_success and genie_space_oauth_success and start_conversation_oauth_success:
        print("✅ OAuth authentication works for all Genie API calls")
    else:
        print("❌ OAuth authentication fails for some Genie API calls:")
        print(f"   - Genie Spaces List: {'✅' if genie_spaces_oauth_success else '❌'}")
        print(f"   - Specific Genie Space: {'✅' if genie_space_oauth_success else '❌'}")
        print(f"   - Start Conversation: {'✅' if start_conversation_oauth_success else '❌'}")
        
        # Check if PAT works for the same endpoints
        genie_spaces_pat_success = comparison_results.get("Genie Spaces List", {}).get("pat", {}).get("success", False)
        genie_space_pat_success = comparison_results.get("Specific Genie Space", {}).get("pat", {}).get("success", False)
        start_conversation_pat_success = comparison_results.get("Start Conversation", {}).get("pat", {}).get("success", False)
        
        if genie_spaces_pat_success and genie_space_pat_success and start_conversation_pat_success:
            print("\n⚠️ PAT authentication works for all Genie API calls, but OAuth fails")
            print("\nThis suggests a permission issue with the service principal.")
            
            # Provide specific recommendations
            print("\nRecommendations:")
            print("1. Verify that the service principal has been granted 'Can View' permission on the Genie space")
            print("2. Try granting 'Can Manage' permission to the service principal for testing purposes")
            print("3. Check if the service principal has the necessary workspace-level permissions")
            print("4. Ensure the service principal is a member of groups with appropriate permissions")
            print("5. Try creating a new Genie space and grant the service principal permissions on it")
            print("6. Contact Databricks support for assistance with OAuth permissions for Genie API")
        else:
            print("\n⚠️ Both PAT and OAuth authentication fail for some Genie API calls")
            print("\nThis suggests an issue with the Genie space or API configuration.")
            
            print("\nRecommendations:")
            print("1. Verify that the Genie space ID is correct")
            print("2. Check if the Genie space is accessible through the Databricks UI")
            print("3. Try creating a new Genie space and test with both PAT and OAuth")
            print("4. Contact Databricks support for assistance with Genie API access")

if __name__ == "__main__":
    main()
