# Essential Files to Keep

## Core Application Files

### Original Web App (web_app/)
```
web_app/
├── app.py                 # Main Flask application
├── templates/
│   ├── base.html         # Base template
│   ├── index.html        # Landing page
│   ├── auth/             # Authentication templates
│   ├── conversation/     # Conversation templates
│   └── partials/         # Partial templates
└── static/
    ├── css/              # Stylesheets
    ├── js/               # JavaScript files
    └── img/              # Images
```

### Web App Version 1 (OAuth Federation)
```
web_app_v1/
├── app.py                # Main Flask application with federation
├── auth_federation.py    # OAuth federation logic
├── auth_routes.py        # Authentication routes
├── admin_routes.py       # Admin dashboard routes
├── models.py             # Database models
├── oauth_auth.py         # OAuth utilities
├── okta_auth.py          # Okta integration
├── federation_api.py     # Federation API endpoints
├── config.yaml           # Configuration file
├── config.yaml.example   # Configuration template
├── requirements.txt      # Python dependencies
├── run_app.py            # Application runner
├── templates/
│   ├── base.html         # Base template
│   ├── auth/             # Authentication templates
│   ├── admin/            # Admin templates
│   └── conversation/     # Conversation templates
├── static/
│   ├── css/              # Stylesheets
│   ├── js/               # JavaScript files
│   └── img/              # Images
└── instance/
    └── genie_federation.db # SQLite database
```

### Web App Version 2 (Config-based)
```
web_app_v2/
├── app.py                # Main Flask application
├── oauth_auth.py         # OAuth utilities
├── config.yaml           # Configuration file
├── config.yaml.example   # Configuration template
├── requirements.txt      # Python dependencies
├── run_app.py            # Application runner
├── templates/
│   ├── base.html         # Base template
│   ├── error.html        # Error page
│   └── conversation/     # Conversation templates
└── static/
    ├── css/              # Stylesheets
    ├── js/               # JavaScript files
    └── img/              # Images
```

### Web App Version 3 (Authentication Library)
```
web_app_v3/
├── demo_app.py           # Demo application
├── genie_auth.py         # Authentication library
├── config.yaml           # Configuration file
├── config.yaml.example   # Configuration template
├── requirements.txt      # Python dependencies
├── run_demo.py           # Demo runner
├── templates/
│   ├── base.html         # Base template
│   ├── index.html        # Demo landing page
│   └── conversation/     # Conversation templates
└── static/
    ├── css/              # Stylesheets
    ├── js/               # JavaScript files
    └── img/              # Images
```

## Configuration Files
```
requirements.txt          # Root dependencies
README.md                 # Main project documentation
```

## Assets
```
assets/
├── databricks.png        # Databricks logo
├── databricks_logo.png   # Alternative Databricks logo
├── databricksicon.png    # Databricks icon
├── tudip.jpeg            # Tudip logo
├── favicon.ico           # Favicon
└── databricks_theme.css  # Databricks theme styles
```

## Deployment
```
deployment/
├── README.md             # Deployment documentation
├── configure_nginx.sh    # Nginx configuration script
├── deploy.sh             # Main deployment script
├── deploy_to_existing_server.sh # Server deployment
├── production.env        # Production environment variables
├── quick_deploy.sh       # Quick deployment
├── setup_ssl.sh          # SSL setup
└── start_services.sh     # Service startup
```

## Key Endpoints Summary

### Original Web App (web_app/app.py)
- `GET /` - Landing page (redirects to login)
- `GET,POST /login` - Login page (PAT/OAuth)
- `GET,POST /login/<auth_type>` - Specific auth type login
- `GET /conversation` - Main conversation interface
- `POST /api/send-message` - Send message to Genie
- `POST /api/start-conversation` - Start new conversation
- `POST /api/new_conversation` - Clear conversation
- `POST /api/switch-auth` - Switch authentication method
- `GET /api/get-message` - Get message status

### Web App V1 (OAuth Federation)
- `GET /` - Landing page (redirects to login)
- `GET,POST /login` - SSO-style login
- `GET /admin/dashboard` - Admin dashboard
- `GET,POST /admin/workspaces` - Workspace management
- `GET,POST /admin/users` - User management
- `GET /conversation` - Conversation interface
- `POST /api/send-message` - Send message to Genie
- `POST /api/federation/token` - Get federation token
- `POST /logout` - Logout

### Web App V2 (Config-based)
- `GET /` - Auto-authenticate and redirect
- `GET /conversation` - Main conversation interface
- `POST /api/send-message` - Send message to Genie

### Web App V3 (Authentication Library)
- `GET /` - Demo landing page
- `GET /api/test-auth` - Test authentication
- `GET /api/token-info` - Get token information

## Authentication Methods

### Original Web App:
- **PAT**: Personal Access Token authentication
- **OAuth**: Service Principal OAuth authentication

### Web App V1:
- **OAuth Federation**: U2M (User-to-Machine) authentication
- **SSO-style**: Email/password login with JWT tokens
- **Role-based**: Admin/User access control

### Web App V2:
- **Config-based**: OAuth credentials from config file
- **Auto-authentication**: No login UI required

### Web App V3:
- **Library**: Reusable authentication component
- **Token caching**: Automatic token management
- **OAuth**: Service Principal authentication

## Database Schema (Web App V1)

### Users Table:
- id (Primary Key)
- email (Unique)
- password_hash
- role (admin/user)
- created_at
- updated_at

### Workspaces Table:
- id (Primary Key)
- name
- host
- space_id
- client_id
- client_secret
- account_id
- created_by (Foreign Key to Users)
- created_at
- updated_at

### User Workspace Access Table:
- id (Primary Key)
- user_id (Foreign Key to Users)
- workspace_id (Foreign Key to Workspaces)
- granted_at
- granted_by (Foreign Key to Users)

## Configuration Structure

All versions use similar YAML configuration:
```yaml
auth:
  host: "databricks-workspace-host"
  space_id: "genie-space-id"
  client_id: "oauth-client-id"
  client_secret: "oauth-client-secret"
  account_id: "databricks-account-id"

ui:
  title: "Application Title"
  colors:
    primary: "#FF3621"
    secondary: "#1B3139"
  logos:
    databricks: "databricks.png"
    tudip: "tudip.jpeg"

flask:
  secret_key: "flask-secret-key"
  debug: false
  host: "0.0.0.0"
  port: 5001
```
