# Troubleshooting Databricks Genie API Issues

## Common Issues and Solutions

### 1. "ENDPOINT_NOT_FOUND" Error

**Error Message:**
```
Error: {"error_code":"ENDPOINT_NOT_FOUND","message":"No API found for 'POST /genie/spaces/01f02f16a7b11b36a04e4353814a5699'"}
```

**Cause:**
This error occurs when the URL for the Genie API is not correctly formatted. The most common issue is that the Space ID includes a query parameter (`?o=1883526265026134`) which needs to be handled specially in the URL construction.

**Solution:**
The scripts have been updated to properly handle Space IDs that include organization ID query parameters. The fix:

1. Extracts the base Space ID (before any `?` character)
2. Extracts the organization ID (after `o=`)
3. Constructs the base URL without the endpoint
4. Adds the organization ID as a query parameter to each endpoint URL separately

```python
# Extract the space ID without any query parameters
SPACE_ID = DATABRICKS_SPACE_ID.split('?')[0] if '?' in DATABRICKS_SPACE_ID else DATABRICKS_SPACE_ID
ORG_ID = None

# Check if there's an organization ID in the space ID
if '?' in DATABRICKS_SPACE_ID and 'o=' in DATABRICKS_SPACE_ID:
    ORG_ID = DATABRICKS_SPACE_ID.split('o=')[1] if 'o=' in DATABRICKS_SPACE_ID else None

# Construct the base URL without the endpoint
BASE_URL = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{SPACE_ID}"

# When making API calls, add the organization ID to each endpoint URL
def start_conversation(question):
    # Construct the URL with the endpoint and query parameter if needed
    endpoint = "/start-conversation"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    # Make the API call
    response = requests.post(url, headers=HEADERS, json={"content": question})
```

The key insight is that the organization ID query parameter (`?o=1883526265026134`) needs to be added to each endpoint URL separately, not to the base URL. This ensures that the query parameter is properly positioned at the end of the URL, after the path components.

### 2. Incorrect API Endpoint Format

**Issue:**
The Genie API endpoints might have changed or be different from what's documented.

**Solution:**
Check the latest Databricks documentation for the correct API endpoints. The current format is:

- Start Conversation: `/api/2.0/genie/spaces/{space_id}/start-conversation`
- Send Follow-up: `/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages`
- Get Message Status: `/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}`
- Get Query Results: `/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}/attachments/{attachment_id}/query-result`

### 3. Authentication Issues

**Issue:**
Authentication failures can occur if the token is invalid or expired.

**Solution:**
- Generate a new personal access token in Databricks
- Ensure the token has the necessary permissions
- Check that the token is correctly formatted in the `.env` file

### 4. Rate Limiting

**Issue:**
Too many requests in a short period can trigger rate limiting.

**Solution:**
- Implement exponential backoff for retries
- Add delays between requests
- Monitor response headers for rate limit information

## Debugging Tips

1. **Check the URL Construction:**
   Print the full URL before making the request to ensure it's correctly formatted.

2. **Inspect Response Headers:**
   Response headers can provide additional information about errors.

3. **Enable Verbose Logging:**
   Use a tool like `requests-toolbelt` to log the full HTTP request and response.

4. **Verify Credentials:**
   Ensure your Databricks host, token, and Space ID are correct.

5. **Check API Documentation:**
   The Genie API is in Public Preview and may change. Always refer to the latest documentation.

## Additional Resources

- [Databricks Genie API Documentation](https://docs.databricks.com/en/genie/conversation-api)
- [Databricks REST API Reference](https://docs.databricks.com/api/workspace/introduction)
- [Databricks Authentication](https://docs.databricks.com/dev-tools/auth.html)
