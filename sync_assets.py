#!/usr/bin/env python3
"""
Script to synchronize assets (images, CSS, JS) across all Genie web app versions
to ensure consistent branding and design.
"""

import os
import shutil
from pathlib import Path

# Define the versions to sync
VERSIONS = [
    "web_app",
    "web_app_v1",
    "web_app_v2",
    "web_app_v3"
]

# Define the assets to sync
ASSETS = {
    "img": [
        "databricks.png",
        "databricksicon.png",
        "databricksicon.svg",
        "favicon.ico",
        "favicon.svg",
        "tudip.jpeg"
    ],
    "css": [
        "style.css"
    ],
    "js": [
        "main.js"
    ]
}

def ensure_directory_exists(directory):
    """Ensure the directory exists, create it if it doesn't"""
    if not os.path.exists(directory):
        os.makedirs(directory)
        print(f"Created directory: {directory}")

def copy_file(source, destination):
    """Copy a file from source to destination"""
    try:
        shutil.copy2(source, destination)
        print(f"Copied: {source} -> {destination}")
    except FileNotFoundError:
        print(f"Warning: Source file not found: {source}")
    except Exception as e:
        print(f"Error copying {source} to {destination}: {str(e)}")

def sync_assets():
    """Synchronize assets across all versions"""
    # Choose the reference version (source of truth)
    reference_version = "web_app_v2"  # Using v2 as the reference

    print(f"Using {reference_version} as the reference for assets")

    # Process each asset type
    for asset_type, files in ASSETS.items():
        for file in files:
            source_path = os.path.join(reference_version, "static", asset_type, file)

            # Skip if source file doesn't exist
            if not os.path.exists(source_path):
                print(f"Warning: Source file not found: {source_path}")
                continue

            # Copy to each version
            for version in VERSIONS:
                if version == reference_version:
                    continue  # Skip the reference version

                destination_dir = os.path.join(version, "static", asset_type)
                ensure_directory_exists(destination_dir)

                destination_path = os.path.join(destination_dir, file)
                copy_file(source_path, destination_path)

def sync_templates():
    """Synchronize template files for consistent UI"""
    # Define template files to sync with their reference versions
    template_files = {
        "base.html": "web_app",
        os.path.join("conversation", "index.html"): "web_app_v2"
    }

    print("Starting template synchronization with multiple references:")

    # Copy templates to each version
    for template_file, reference_version in template_files.items():
        print(f"Using {reference_version} as reference for {template_file}")
        source_path = os.path.join(reference_version, "templates", template_file)

        # Skip if source file doesn't exist
        if not os.path.exists(source_path):
            print(f"Warning: Source template not found: {source_path}")
            continue

        # Copy to each version
        for version in VERSIONS:
            if version == reference_version:
                continue  # Skip the reference version

            destination_dir = os.path.join(version, "templates", os.path.dirname(template_file))
            ensure_directory_exists(destination_dir)

            destination_path = os.path.join(version, "templates", template_file)
            copy_file(source_path, destination_path)

def main():
    """Main function"""
    print("Starting asset synchronization...")
    sync_assets()

    print("\nStarting template synchronization...")
    sync_templates()

    print("\nSynchronization complete!")

if __name__ == "__main__":
    main()
