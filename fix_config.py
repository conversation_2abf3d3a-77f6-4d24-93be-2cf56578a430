#!/usr/bin/env python3
"""
Fix Configuration Files for Databricks Genie Web Applications

This script checks and fixes configuration files for all versions of the Databricks Genie Web Application.
It ensures that config.yaml files exist and contain valid configuration.

Usage:
    python fix_config.py
"""

import os
import sys
import shutil
import yaml

def check_and_fix_config(version_dir):
    """Check and fix config.yaml file for a specific version"""
    print(f"\nChecking configuration for {version_dir}...")
    
    config_path = os.path.join(version_dir, "config.yaml")
    example_path = os.path.join(version_dir, "config.yaml.example")
    
    # Check if config file exists
    if not os.path.exists(config_path):
        # Check if example file exists
        if os.path.exists(example_path):
            print(f"Config file not found. Creating from example...")
            shutil.copy(example_path, config_path)
            print(f"Created {config_path}. Please edit it with your settings.")
        else:
            print(f"Error: config.yaml not found and no example file available.")
            print(f"Please create a config.yaml file with your settings.")
            return False
    
    # Validate config file
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Check for required fields
        auth_config = config.get('auth', {})
        host = auth_config.get('host', '')
        space_id = auth_config.get('space_id', '')
        client_id = auth_config.get('client_id', '')
        client_secret = auth_config.get('client_secret', '')
        
        if not host:
            print(f"Warning: 'host' is not set in {config_path}")
            return False
        
        if not space_id:
            print(f"Warning: 'space_id' is not set in {config_path}")
            return False
        
        if not client_id:
            print(f"Warning: 'client_id' is not set in {config_path}")
            return False
        
        if not client_secret:
            print(f"Warning: 'client_secret' is not set in {config_path}")
            return False
        
        print(f"Configuration for {version_dir} is valid.")
        return True
    
    except Exception as e:
        print(f"Error validating config: {str(e)}")
        return False

def main():
    """Main function"""
    print("Databricks Genie Web Application Configuration Fixer")
    print("==================================================")
    
    # Check and fix config for each version
    versions = ["web_app_v1", "web_app_v2", "web_app_v3"]
    
    for version in versions:
        if os.path.exists(version):
            check_and_fix_config(version)
        else:
            print(f"\n{version} directory not found. Skipping.")
    
    print("\nConfiguration check complete.")

if __name__ == "__main__":
    main()
