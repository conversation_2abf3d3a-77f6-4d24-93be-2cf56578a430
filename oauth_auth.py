#!/usr/bin/env python3
"""
Databricks Genie API OAuth Authentication

This module handles OAuth authentication for the Databricks Genie API.
It supports both service principal and user authentication.

Usage:
    from oauth_auth import get_oauth_token
    token = get_oauth_token()
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
"""

import os
import time
import json
import requests
from dotenv import load_dotenv

# Load environment variables
from dotenv import find_dotenv, dotenv_values
dotenv_path = find_dotenv()

# Try to load environment variables directly from the .env file
env_vars = dotenv_values(dotenv_path)

# Set environment variables directly
for key, value in env_vars.items():
    # Remove quotes if present in the value
    if value and isinstance(value, str):
        # Strip quotes if they exist
        if (value.startswith("'") and value.endswith("'")) or (value.startswith('"') and value.endswith('"')):
            value = value[1:-1]
        os.environ[key] = value

# Now load with load_dotenv
load_dotenv(dotenv_path)

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")
DATABRICKS_ACCOUNT_ID = os.getenv("DATABRICKS_ACCOUNT_ID")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")


# Extract space ID and org ID if present
SPACE_ID = DATABRICKS_SPACE_ID.split('?')[0] if '?' in DATABRICKS_SPACE_ID else DATABRICKS_SPACE_ID
ORG_ID = None

# Extract organization ID if present
if '?' in DATABRICKS_SPACE_ID and 'o=' in DATABRICKS_SPACE_ID:
    ORG_ID = DATABRICKS_SPACE_ID.split('o=')[1] if 'o=' in DATABRICKS_SPACE_ID else None

# Remove any 'datarooms/' prefix if it exists in the SPACE_ID
if SPACE_ID.startswith('datarooms/'):
    SPACE_ID = SPACE_ID.replace('datarooms/', '')

# Token cache
_token_cache = {
    "access_token": None,
    "expires_at": 0
}

def get_oauth_token():
    """
    Get an OAuth token for authenticating with the Databricks API.

    This function will:
    1. Check if there's a valid cached token
    2. If not, request a new token
    3. Cache the token and return it

    Returns:
        str: The OAuth access token
    """
    global _token_cache

    # Check if we have a valid cached token
    current_time = time.time()
    if _token_cache["access_token"] and _token_cache["expires_at"] > current_time + 60:
        return _token_cache["access_token"]

    # No valid token, request a new one
    token = _request_oauth_token()

    # Cache the token
    _token_cache["access_token"] = token["access_token"]
    _token_cache["expires_at"] = current_time + token["expires_in"]

    return _token_cache["access_token"]

def _request_oauth_token():
    """
    Request a new OAuth token from the Databricks API.

    Returns:
        dict: The token response containing access_token, token_type, and expires_in
    """
    # Check if we have the required environment variables
    if not all([DATABRICKS_HOST, DATABRICKS_CLIENT_ID, DATABRICKS_CLIENT_SECRET]):
        raise ValueError(
            "Missing required environment variables. "
            "Please set DATABRICKS_HOST, DATABRICKS_CLIENT_ID, and DATABRICKS_CLIENT_SECRET."
        )

    # Determine if we're using account-level or workspace-level authentication
    if DATABRICKS_ACCOUNT_ID:
        # Account-level token endpoint
        token_endpoint = f"https://accounts.cloud.databricks.com/oidc/accounts/{DATABRICKS_ACCOUNT_ID}/v1/token"
    else:
        # Workspace-level token endpoint
        token_endpoint = f"https://{DATABRICKS_HOST}/oidc/v1/token"

    # Request the token
    response = requests.post(
        token_endpoint,
        auth=(DATABRICKS_CLIENT_ID, DATABRICKS_CLIENT_SECRET),
        data={"grant_type": "client_credentials", "scope": "all-apis"}
    )

    # Check if the request was successful
    if response.status_code != 200:
        raise Exception(f"Failed to get OAuth token: {response.text}")

    # Return the token response
    return response.json()

def get_auth_headers():
    """
    Get the authorization headers for Databricks API requests.

    Returns:
        dict: The headers to use for API requests
    """
    token = get_oauth_token()
    return {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

def get_genie_space_url(space_id=None):
    """
    Get the URL for accessing a specific Genie space.

    Args:
        space_id (str, optional): The ID of the Genie space. Defaults to the space ID from the .env file.

    Returns:
        str: The URL for accessing the Genie space
    """
    if not space_id:
        space_id = SPACE_ID

    # Use the correct URL format for accessing a specific Genie space
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}"

    # Add organization ID as a query parameter if available
    if ORG_ID:
        url += f"?o={ORG_ID}"

    return url

def get_start_conversation_url(space_id=None):
    """
    Get the URL for starting a conversation in a Genie space.

    Args:
        space_id (str, optional): The ID of the Genie space. Defaults to the space ID from the .env file.

    Returns:
        str: The URL for starting a conversation
    """
    if not space_id:
        space_id = SPACE_ID

    # Use the correct URL format for starting a conversation
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation"

    # Add organization ID as a query parameter if available
    if ORG_ID:
        url += f"?o={ORG_ID}"

    return url

def get_message_url(conversation_id, message_id, space_id=None):
    """
    Get the URL for accessing a message in a conversation.

    Args:
        conversation_id (str): The ID of the conversation
        message_id (str): The ID of the message
        space_id (str, optional): The ID of the Genie space. Defaults to the space ID from the .env file.

    Returns:
        str: The URL for accessing the message
    """
    if not space_id:
        space_id = SPACE_ID

    # Use the correct URL format for accessing a message
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}"

    # Add organization ID as a query parameter if available
    if ORG_ID:
        url += f"?o={ORG_ID}"

    return url

def get_send_message_url(conversation_id, space_id=None):
    """
    Get the URL for sending a message to a conversation.

    Args:
        conversation_id (str): The ID of the conversation
        space_id (str, optional): The ID of the Genie space. Defaults to the space ID from the .env file.

    Returns:
        str: The URL for sending a message
    """
    if not space_id:
        space_id = SPACE_ID

    # Use the correct URL format for sending a message
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages"

    # Add organization ID as a query parameter if available
    if ORG_ID:
        url += f"?o={ORG_ID}"

    return url

if __name__ == "__main__":
    # Test the OAuth token functionality
    try:
        token = get_oauth_token()
        print(f"Successfully obtained OAuth token: {token[:10]}...")

        # Test the token with a simple API call
        headers = get_auth_headers()

        # Test with workspace-level API calls
        # First try a simple API call to check token validity
        url = f"https://{DATABRICKS_HOST}/api/2.0/clusters/list"

        print(f"Testing token with standard API call to: {url}")
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            print("Standard API call successful!")
            print(f"Response: {json.dumps(response.json(), indent=2)[:200]}...")
        else:
            print(f"Standard API call failed with status code {response.status_code}")
            print(f"Response: {response.text}")

        # Test the Genie space API using the correct URL format
        genie_url = get_genie_space_url()
        print(f"\nTesting token with Genie space API: {genie_url}")
        genie_response = requests.get(genie_url, headers=headers)

        if genie_response.status_code == 200:
            print("Genie space API call successful!")
            print(f"Response: {json.dumps(genie_response.json(), indent=2)}")
        else:
            print(f"Genie space API call failed with status code {genie_response.status_code}")
            print(f"Response: {genie_response.text}")

        # Test the start-conversation API using the correct URL format
        start_conversation_url = get_start_conversation_url()
        print(f"\nTesting token with start-conversation API: {start_conversation_url}")

        # Use POST for start-conversation endpoint
        payload = {"content": "List the first 5 rows from the sales_transactions table in the samples.bakehouse schema"}
        start_conversation_response = requests.post(start_conversation_url, headers=headers, json=payload)

        if start_conversation_response.status_code == 200:
            print("Start conversation API call successful!")
            data = start_conversation_response.json()
            print(f"Conversation ID: {data.get('conversation_id')}")
            print(f"Message ID: {data.get('message_id')}")

            # Get the conversation and message IDs
            conversation_id = data.get("conversation_id")
            message_id = data.get("message_id")

            if conversation_id and message_id:
                # Wait for the message to complete
                print("\nWaiting for message to complete...")
                max_wait_seconds = 60
                poll_interval = 2
                start_time = time.time()

                while time.time() - start_time < max_wait_seconds:
                    # Get the message status
                    message_url = get_message_url(conversation_id, message_id)
                    message_response = requests.get(message_url, headers=headers)

                    if message_response.status_code == 200:
                        message_data = message_response.json()
                        status = message_data.get("status")
                        print(f"Message status: {status}")

                        if status == "COMPLETED":
                            print("Message completed!")
                            print(f"Response: {json.dumps(message_data, indent=2)[:500]}...")
                            break
                        elif status in ["SUBMITTED", "PROCESSING", "ASKING_AI", "PENDING_WAREHOUSE"]:
                            print(f"Message is still processing (status: {status})...")
                            time.sleep(poll_interval)
                        else:
                            print(f"Unknown status: {status}")
                            break
                    else:
                        print(f"Failed to get message status: {message_response.status_code}")
                        print(f"Response: {message_response.text}")
                        break
        else:
            print(f"Start conversation API call failed with status code {start_conversation_response.status_code}")
            print(f"Response: {start_conversation_response.text}")

    except Exception as e:
        print(f"Error: {str(e)}")
