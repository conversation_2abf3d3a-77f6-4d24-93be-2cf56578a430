#!/usr/bin/env python3
"""
Test OAuth Authentication URL Formats with Databricks Genie API

This script tests different URL formats for OAuth authentication with the Databricks Genie API
to determine which format returns a 403 permission denied error instead of a 404 not found error.
"""

import os
import json
import requests
from dotenv import load_dotenv

# Import OAuth authentication module
try:
    from oauth_auth import get_auth_headers
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID", "01f02f16a7b11b36a04e4353814a5699?o=1883526265026134")

# Check if required environment variables are set
if not DATABRICKS_HOST:
    print("Error: Missing required environment variables.")
    print("Please set DATABRICKS_HOST in your .env file.")
    exit(1)

if not OAUTH_AVAILABLE or not DATABRICKS_CLIENT_ID or not DATABRICKS_CLIENT_SECRET:
    print("Error: OAuth authentication not available.")
    print("Please set DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET in your .env file.")
    exit(1)

def test_url_format(format_name, url, method="GET", payload=None):
    """Test a specific URL format"""
    print(f"\n=== Testing Format {format_name} ===")
    print(f"URL: {url}")
    print(f"Method: {method}")

    if payload:
        print(f"Payload: {json.dumps(payload)}")

    # Get OAuth headers
    headers = get_auth_headers()

    try:
        if method == "GET":
            response = requests.get(url, headers=headers)
        elif method == "POST":
            response = requests.post(url, headers=headers, json=payload)
        else:
            print(f"Unsupported method: {method}")
            return

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("Success!")
            try:
                print(f"Response: {json.dumps(response.json(), indent=2)}")
            except json.JSONDecodeError:
                print(f"Response is not in JSON format: {response.text[:200]}...")
        elif response.status_code == 403:
            print("Permission Denied (403) - This is the expected error for OAuth!")
            print(f"Response: {response.text}")
        elif response.status_code == 404:
            print("Not Found (404) - This URL format is incorrect")
            print(f"Response: {response.text}")
        else:
            print(f"Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {str(e)}")

def main():
    """Test different URL formats"""
    print("Testing OAuth Authentication URL Formats with Databricks Genie API")
    print(f"Host: {DATABRICKS_HOST}")

    # Extract space ID and org ID
    space_id_full = DATABRICKS_SPACE_ID
    space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full
    org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

    print(f"Space ID: {space_id}")
    print(f"Organization ID: {org_id}")

    # First test a standard API call to verify OAuth token works
    standard_url = f"https://{DATABRICKS_HOST}/api/2.0/clusters/list"
    test_url_format("Standard API", standard_url)

    # Test different URL formats for the Genie API

    # Format 1: Basic spaces endpoint
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}"
    if org_id:
        url += f"?o={org_id}"
    test_url_format("1 - Basic spaces endpoint", url)

    # Format 2: With 'datarooms/' prefix
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}"
    if org_id:
        url += f"?o={org_id}"
    test_url_format("2 - With datarooms prefix", url)

    # Format 3: Using 'datarooms/' as the space_id
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms"
    if org_id:
        url += f"?o={org_id}"
    test_url_format("3 - Using datarooms as space_id", url)

    # Format 4: Using 'rooms' instead of 'spaces'
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/rooms/{space_id}"
    if org_id:
        url += f"?o={org_id}"
    test_url_format("4 - Using rooms instead of spaces", url)

    # Format 5: Using start-conversation endpoint
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation"
    if org_id:
        url += f"?o={org_id}"
    payload = {"content": "Show me the top 10 customers by revenue"}
    test_url_format("5 - Start conversation endpoint", url, method="POST", payload=payload)

    # Format 6: Using datarooms as space_id with start-conversation
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/start-conversation"
    if org_id:
        url += f"?o={org_id}"
    payload = {"content": "Show me the top 10 customers by revenue"}
    test_url_format("6 - Datarooms with start-conversation", url, method="POST", payload=payload)

    # Format 7: Using just datarooms without space_id
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms"
    if org_id:
        url += f"?o={org_id}"
    test_url_format("7 - Just datarooms", url)

    # Format 8: Using datarooms as the endpoint
    url = f"https://{DATABRICKS_HOST}/api/2.0/genie/datarooms"
    if org_id:
        url += f"?o={org_id}"
    test_url_format("8 - Datarooms as endpoint", url)

if __name__ == "__main__":
    main()
