# Databricks Genie Web Application - Cloud Deployment Summary

## Deployment Information

**Server Details:**
- Server: `dev2.tudip.com`
- Domain: `databricks-accelerator.tudip.ai`
- SSH User: `root`
- SSH Password: `Tud!pd3v2adm!n`

**Application Details:**
- Version: Web App V1 (OAuth Authentication)
- Port: 5001 (internal)
- External Access: Port 80/443 via Nginx reverse proxy
- Service Name: `databricks-genie`
- Application User: `genie`
- Installation Directory: `/opt/databricks-genie`

## Quick Deployment (Automated)

For automated deployment, run the quick deployment script:

```bash
cd deployment
./quick_deploy.sh
```

This script will:
1. Test connection to the server
2. Copy all project files to the server
3. Run the deployment script
4. Configure Nginx
5. Start all services
6. Test the deployment

## Manual Deployment Steps

If you prefer manual deployment:

### 1. Connect to Server
```bash
ssh <EMAIL>
# Password: Tud!pd3v2adm!n
```

### 2. Upload Project Files
```bash
# From your local machine
scp -r /path/to/Genie_API_Exploration <EMAIL>:/tmp/
```

### 3. Run Deployment Scripts
```bash
# On the server
cd /tmp/Genie_API_Exploration/deployment
chmod +x *.sh

# Deploy application
./deploy.sh

# Configure Nginx
./configure_nginx.sh

# Start services
./start_services.sh

# Set up SSL (optional but recommended)
./setup_ssl.sh
```

## Post-Deployment Verification

### 1. Check Service Status
```bash
sudo systemctl status databricks-genie
sudo systemctl status nginx
```

### 2. View Application Logs
```bash
sudo journalctl -u databricks-genie -f
```

### 3. Test Application
- Open browser to: `http://databricks-accelerator.tudip.ai`
- Should see the OAuth login page
- Test authentication with your Databricks credentials

### 4. Check Nginx Logs
```bash
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## Configuration Files

### Application Configuration
- **Main Config**: `/opt/databricks-genie/config.yaml`
- **Environment**: `/opt/databricks-genie/.env`
- **Application**: `/opt/databricks-genie/app.py`

### System Configuration
- **Systemd Service**: `/etc/systemd/system/databricks-genie.service`
- **Nginx Config**: `/etc/nginx/sites-available/databricks-genie`

## Environment Variables

The application uses these environment variables (configured in `/opt/databricks-genie/.env`):

```bash
DATABRICKS_HOST=dbc-620d1468-0f52.cloud.databricks.com
DATABRICKS_CLIENT_ID=9e4f6f53-e2fb-4f7c-a7ce-bf1889db9bdc
DATABRICKS_CLIENT_SECRET=dosebc6041c5004a2289159df91d98a97a66
DATABRICKS_ACCOUNT_ID=3d0c3701-02ec-4f84-a472-d2613e2bfcf1
DATABRICKS_SPACE_ID=01f02f16a7b11b36a04e4353814a5699?o=****************
CURRENT_AUTH_TYPE=oauth
FLASK_SECRET_KEY=prod-key-databricks-genie-tudip-2024-secure
FLASK_ENV=production
FLASK_DEBUG=False
APP_HOST=0.0.0.0
APP_PORT=5001
```

## Management Commands

### Application Management
```bash
# Start application
sudo systemctl start databricks-genie

# Stop application
sudo systemctl stop databricks-genie

# Restart application
sudo systemctl restart databricks-genie

# Enable auto-start on boot
sudo systemctl enable databricks-genie

# Disable auto-start
sudo systemctl disable databricks-genie
```

### Nginx Management
```bash
# Test configuration
sudo nginx -t

# Reload configuration
sudo systemctl reload nginx

# Restart Nginx
sudo systemctl restart nginx
```

### SSL Certificate (Let's Encrypt)
```bash
# Check certificate status
sudo certbot certificates

# Renew certificates
sudo certbot renew

# Test renewal
sudo certbot renew --dry-run
```

## Troubleshooting

### Common Issues

1. **Application won't start**
   - Check logs: `sudo journalctl -u databricks-genie -f`
   - Verify config: `sudo cat /opt/databricks-genie/config.yaml`
   - Check permissions: `ls -la /opt/databricks-genie/`

2. **Nginx 502 Bad Gateway**
   - Check if application is running: `sudo systemctl status databricks-genie`
   - Verify port 5001 is listening: `sudo netstat -tlnp | grep :5001`
   - Check Nginx config: `sudo nginx -t`

3. **SSL Certificate issues**
   - Verify DNS: `dig databricks-accelerator.tudip.ai`
   - Check firewall: `sudo ufw status`
   - Review certbot logs: `sudo tail -f /var/log/letsencrypt/letsencrypt.log`

### Log Locations
- **Application Logs**: `sudo journalctl -u databricks-genie`
- **Nginx Access**: `/var/log/nginx/access.log`
- **Nginx Error**: `/var/log/nginx/error.log`
- **SSL Certificate**: `/var/log/letsencrypt/letsencrypt.log`

## Security Features

1. **OAuth Authentication**: Only OAuth authentication is enabled (no PAT)
2. **HTTPS Encryption**: SSL certificate from Let's Encrypt
3. **Non-root User**: Application runs as `genie` user
4. **Firewall**: Only ports 22, 80, 443 should be open
5. **Security Headers**: Nginx configured with security headers

## Backup and Recovery

### Backup Application
```bash
sudo tar -czf /backup/databricks-genie-$(date +%Y%m%d).tar.gz /opt/databricks-genie
```

### Restore Application
```bash
sudo systemctl stop databricks-genie
sudo tar -xzf /backup/databricks-genie-YYYYMMDD.tar.gz -C /
sudo chown -R genie:genie /opt/databricks-genie
sudo systemctl start databricks-genie
```

## Support

For issues or questions:
- Check this documentation first
- Review application logs
- Contact: <EMAIL>

## Success Criteria

Deployment is successful when:
1. ✅ Application service is running (`systemctl status databricks-genie`)
2. ✅ Nginx is running and configured (`systemctl status nginx`)
3. ✅ Domain resolves to server (`dig databricks-accelerator.tudip.ai`)
4. ✅ HTTP/HTTPS endpoints respond (`curl -I http://databricks-accelerator.tudip.ai`)
5. ✅ OAuth login page loads in browser
6. ✅ Authentication works with Databricks credentials
7. ✅ SSL certificate is valid (if configured)

**Final URL**: `https://databricks-accelerator.tudip.ai`
