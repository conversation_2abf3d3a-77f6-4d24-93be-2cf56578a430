#!/usr/bin/env python3
"""
Databricks Genie Launcher

This script serves as the main entry point for the Databricks Genie application.
It checks for existing authentication and either launches the main app directly
or shows the login UI first.

Usage:
    python genie_launcher.py
"""

import os
import sys

# Try to import required packages
try:
    from dotenv import load_dotenv, find_dotenv, dotenv_values
    import requests
    REQUIRED_PACKAGES_AVAILABLE = True
except ImportError:
    REQUIRED_PACKAGES_AVAILABLE = False
    print("Error: Required packages are not installed.")
    print("To install required packages:")
    print("  pip install python-dotenv requests")
    print("\nAlternatively, you can install all requirements:")
    print("  pip install -r requirements.txt")
    sys.exit(1)

# Try to import tkinter
try:
    import tkinter as tk
    TKINTER_AVAILABLE = True
except ImportError:
    TKINTER_AVAILABLE = False
    print("Error: tkinter is not installed. The GUI requires tkinter.")
    print("To install tkinter:")
    print("  - On Ubuntu/Debian: sudo apt-get install python3-tk")
    print("  - On Fedora/RHEL: sudo dnf install python3-tkinter")
    print("  - On macOS: brew install python-tk")
    print("  - On Windows: tkinter is included with Python by default")
    print("\nFalling back to command-line mode...")

# Load environment variables
dotenv_path = find_dotenv()
if not dotenv_path:
    dotenv_path = '.env'

# Load environment variables directly from the .env file
env_vars = dotenv_values(dotenv_path)

# Set environment variables directly
for key, value in env_vars.items():
    if value:
        os.environ[key] = value

# Now load with load_dotenv
load_dotenv(dotenv_path)

def check_authentication():
    """
    Check if we have valid authentication credentials.
    Returns the authentication type if valid, None otherwise.
    """
    host = os.getenv("DATABRICKS_HOST")
    if not host:
        return None

    # Check PAT authentication
    token = os.getenv("DATABRICKS_TOKEN")
    if token:
        try:
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

            # Test with a simple API call
            url = f"https://{host}/api/2.0/clusters/list"
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                return "pat"
        except:
            pass

    # Check OAuth authentication
    client_id = os.getenv("DATABRICKS_CLIENT_ID")
    client_secret = os.getenv("DATABRICKS_CLIENT_SECRET")

    if client_id and client_secret:
        try:
            # Try to import OAuth module
            from oauth_auth import get_auth_headers

            # Test with a simple API call
            headers = get_auth_headers()
            url = f"https://{host}/api/2.0/clusters/list"
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                return "oauth"
        except:
            pass

    # No valid authentication found
    return None

def main():
    """Main function"""
    # Check if we have valid authentication
    auth_type = check_authentication()

    if not TKINTER_AVAILABLE:
        # Tkinter is not available, fall back to command-line mode
        if auth_type:
            print(f"Authentication found: {auth_type}")
            print("To use the Genie API in command-line mode, run:")
            print("  python genie_api_explorer.py")
        else:
            print("No valid authentication found.")
            print("Please configure authentication using:")
            print("  1. Edit the .env file directly")
            print("  2. Install tkinter and run this script again")
        return

    # Create the root window
    root = tk.Tk()

    if auth_type:
        # We have valid authentication, launch the main app directly
        from genie_app import GenieApp
        app = GenieApp(root, auth_type=auth_type)
    else:
        # No valid authentication, show the login UI
        from login_ui import LoginUI
        app = LoginUI(root)

    # Start the main loop
    root.mainloop()

if __name__ == "__main__":
    main()
