# OAuth Authentication for Databricks Genie API

This document explains how to set up and use OAuth authentication with the Databricks Genie API explorer scripts.

## Overview

The scripts now support two authentication methods:

1. **Personal Access Token (PAT)** - The original method using a Databricks personal access token
2. **OAuth Authentication** - The recommended method using OAuth 2.0 with a service principal

OAuth authentication is more secure and provides better token management with automatic token refresh.

## Prerequisites

To use OAuth authentication, you need to:

1. Create a service principal in your Databricks workspace
2. Generate an OAuth secret for the service principal
3. Configure the environment variables with the service principal credentials

## Step 1: Create a Service Principal

1. Log in to your Databricks workspace as an admin
2. Click your username in the top bar and select **Settings**
3. Click on the **Identity and access** tab
4. Next to **Service principals**, click **Manage**
5. Click **Add service principal**
6. Enter a name for the service principal (e.g., "Genie API Explorer")
7. Click **Add**

## Step 2: Assign Permissions to the Service Principal

1. Click the name of your service principal to open its details page
2. On the **Configurations** tab, check the necessary entitlements
3. On the **Permissions** tab, grant access to the Genie spaces the service principal needs to access

## Step 3: Generate an OAuth Secret

1. On your service principal's details page, click the **Secrets** tab
2. Under **OAuth secrets**, click **Generate secret**
3. Set the secret's lifetime (maximum 730 days / 2 years)
4. Copy the displayed **Secret** and **Client ID**
5. Click **Done**

## Step 4: Configure Environment Variables

Update your `.env` file with the OAuth credentials:

```
# Databricks workspace hostname
DATABRICKS_HOST=dbc-123abc45-6def.cloud.databricks.com

# OAuth credentials (recommended)
DATABRICKS_CLIENT_ID=your-client-id
DATABRICKS_CLIENT_SECRET=your-client-secret
# Only needed for account-level operations (leave empty for workspace-level operations)
DATABRICKS_ACCOUNT_ID=

# Genie Space ID
DATABRICKS_SPACE_ID=your-space-id?o=your-org-id
```

You can keep your existing PAT token in the `.env` file as a fallback:

```
# Personal Access Token (fallback)
DATABRICKS_TOKEN=your-pat-token
```

## How It Works

The scripts now include a new module `oauth_auth.py` that handles OAuth authentication:

1. When the scripts start, they check if OAuth credentials are available
2. If OAuth credentials are available, they use OAuth authentication
3. If not, they fall back to using the PAT token
4. The OAuth module automatically handles token refresh when tokens expire

## Testing OAuth Authentication

You can test if your OAuth configuration is working by running:

```bash
python oauth_auth.py
```

This will attempt to obtain an OAuth token and make a test API call.

## Troubleshooting

### Common Issues

1. **"No authentication method available"** - You need to provide either OAuth credentials or a PAT token
2. **"Failed to get OAuth token"** - Check that your client ID and secret are correct
3. **"ImportError: No module named 'oauth_auth'"** - Make sure the `oauth_auth.py` file is in the same directory as your scripts

### Checking Token Status

To check if your OAuth token is being properly obtained and refreshed, look for these messages in the script output:

```
Using OAuth authentication
```

This indicates that the script is using OAuth authentication instead of PAT.

## Security Considerations

- OAuth secrets have a maximum lifetime of 2 years
- The OAuth module automatically refreshes tokens before they expire
- Store your client secret securely and never commit it to version control
- Consider using environment variables or a secrets manager instead of storing credentials in the `.env` file

## References

- [Databricks OAuth Documentation](https://docs.databricks.com/dev-tools/auth/oauth-m2m)
- [Databricks Service Principals](https://docs.databricks.com/admin/users-groups/service-principals.html)
- [OAuth 2.0 Machine-to-Machine (M2M) Authentication](https://auth0.com/docs/get-started/authentication-and-authorization-flow/client-credentials-flow)
