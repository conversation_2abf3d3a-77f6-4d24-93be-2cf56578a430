#!/usr/bin/env python3
"""
Simple Databricks Genie API Demo

This script demonstrates a basic interaction with the Databricks Genie API
by asking a question, waiting for the response, and displaying the results.

Usage:
    python simple_genie_demo.py "Your question here"

Requirements:
    - Python 3.6+
    - requests
    - python-dotenv
    - tabulate (for displaying results)
"""

import os
import sys
import json
import time
import requests
from dotenv import load_dotenv
from tabulate import tabulate

# Import OAuth authentication module
try:
    from oauth_auth import get_auth_headers
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables from .env file
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")

# Check if required environment variables are set
if not DATABRICKS_HOST or not DATABRICKS_SPACE_ID:
    print("Error: Missing required environment variables.")
    print("Please create a .env file with DATABRICKS_HOST and DATABRICKS_SPACE_ID.")
    sys.exit(1)

# Check authentication method
USE_OAUTH = OAUTH_AVAILABLE and DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET
if not USE_OAUTH and not DATABRICKS_TOKEN:
    print("Error: No authentication method available.")
    print("Please provide either a personal access token (DATABRICKS_TOKEN) or OAuth credentials (DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET).")
    sys.exit(1)

# API Base URL
# Extract the space ID without any query parameters
SPACE_ID = DATABRICKS_SPACE_ID.split('?')[0] if '?' in DATABRICKS_SPACE_ID else DATABRICKS_SPACE_ID
ORG_ID = None

# Check if there's an organization ID in the space ID
if '?' in DATABRICKS_SPACE_ID and 'o=' in DATABRICKS_SPACE_ID:
    ORG_ID = DATABRICKS_SPACE_ID.split('o=')[1] if 'o=' in DATABRICKS_SPACE_ID else None

# Construct the base URL without the endpoint
BASE_URL = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{SPACE_ID}"

# Headers for API requests
if USE_OAUTH:
    print("Using OAuth authentication")
    HEADERS = get_auth_headers()
else:
    print("Using Personal Access Token (PAT) authentication")
    HEADERS = {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }

def start_conversation(question):
    """Start a new conversation with Genie"""
    # Construct the URL with the endpoint and query parameter if needed
    endpoint = "/start-conversation"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    payload = {"content": question}

    print(f"Starting conversation with question: '{question}'")
    response = requests.post(url, headers=HEADERS, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error starting conversation: {response.text}")
        return None

def get_message_status(conversation_id, message_id):
    """Get the status of a message"""
    # Construct the URL with the endpoint and query parameter if needed
    endpoint = f"/conversations/{conversation_id}/messages/{message_id}"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    response = requests.get(url, headers=HEADERS)

    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error getting message status: {response.text}")
        return None

def get_query_result(conversation_id, message_id, attachment_id):
    """Get the query result for a message attachment"""
    # Construct the URL with the endpoint and query parameter if needed
    endpoint = f"/conversations/{conversation_id}/messages/{message_id}/attachments/{attachment_id}/query-result"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    response = requests.get(url, headers=HEADERS)

    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error getting query result: {response.text}")
        return None

def wait_for_message_completion(conversation_id, message_id, max_wait_seconds=300, poll_interval=5):
    """Wait for a message to complete processing"""
    print(f"Waiting for Genie to process your question...")

    start_time = time.time()
    while time.time() - start_time < max_wait_seconds:
        message = get_message_status(conversation_id, message_id)

        if not message:
            return None

        status = message.get("status")

        if status == "COMPLETED":
            print("✓ Processing complete!")
            return message
        elif status in ["FAILED", "ERROR"]:
            print(f"✗ Processing failed with status: {status}")
            if message.get("error"):
                print(f"Error: {message['error']}")
            return message

        # Print a progress indicator
        sys.stdout.write(".")
        sys.stdout.flush()
        time.sleep(poll_interval)

    print(f"\nTimed out after waiting {max_wait_seconds} seconds")
    return None

def display_query_and_results(message, query_result):
    """Display the generated SQL query and results in a readable format"""
    # Extract the SQL query
    attachments = message.get("attachments", [])
    if not attachments:
        print("No query was generated.")
        return

    query_info = attachments[0].get("query", {})
    sql_query = query_info.get("query", "No query available")

    # Print the SQL query
    print("\n=== Generated SQL Query ===")
    print(sql_query)
    print("=========================\n")

    # Print the query results
    if not query_result:
        print("No results available.")
        return

    print("=== Query Results ===")

    # Extract data and format as a table
    data = query_result.get("data", [])
    if not data:
        print("No data returned.")
        return

    # Get column names from the first row
    columns = list(data[0].keys()) if data else []

    # Extract rows
    rows = [[row.get(col) for col in columns] for row in data]

    # Print as a table
    print(tabulate(rows, headers=columns, tablefmt="grid"))
    print(f"\nTotal rows: {len(data)}")

def main():
    """Main function to demonstrate the Genie API"""
    # Display authentication information
    print(f"Authentication Method: {'OAuth' if USE_OAUTH else 'Personal Access Token (PAT)'}")

    # Get the question from command line arguments or use a default
    if len(sys.argv) > 1:
        question = sys.argv[1]
    else:
        question = "Show me the top 5 customers by revenue"

    # Start a conversation
    conversation = start_conversation(question)
    if not conversation:
        sys.exit(1)

    conversation_id = conversation.get("conversation_id")
    message_id = conversation.get("message_id")

    # Wait for the message to complete
    completed_message = wait_for_message_completion(conversation_id, message_id)
    if not completed_message:
        sys.exit(1)

    # Get query results if available
    query_result = None
    attachments = completed_message.get("attachments", [])
    if attachments:
        attachment_id = attachments[0].get("attachment_id")
        query_result = get_query_result(conversation_id, message_id, attachment_id)

    # Display the results
    display_query_and_results(completed_message, query_result)

if __name__ == "__main__":
    main()
