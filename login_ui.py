#!/usr/bin/env python3
"""
Databricks Genie Login UI

This script provides a user-friendly login interface for the Databricks Genie API.
It supports direct username/password login, Personal Access Token (PAT), and OAuth authentication.

Usage:
    python login_ui.py
"""

import os
import sys
import webbrowser

# Try to import required packages
try:
    import requests
    from dotenv import load_dotenv, set_key, find_dotenv, dotenv_values
    REQUIRED_PACKAGES_AVAILABLE = True
except ImportError:
    REQUIRED_PACKAGES_AVAILABLE = False
    print("Error: Required packages are not installed.")
    print("To install required packages:")
    print("  pip install python-dotenv requests")
    print("\nAlternatively, you can install all requirements:")
    print("  pip install -r requirements.txt")
    sys.exit(1)

# Try to import tkinter
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, font
    TKINTER_AVAILABLE = True
except ImportError:
    TKINTER_AVAILABLE = False
    print("Error: tkinter is not installed. This UI requires tkinter.")
    print("To install tkinter:")
    print("  - On Ubuntu/Debian: sudo apt-get install python3-tk")
    print("  - On Fedora/RHEL: sudo dnf install python3-tkinter")
    print("  - On macOS: brew install python-tk")
    print("  - On Windows: tkinter is included with Python by default")
    sys.exit(1)

# Try to import OAuth authentication
try:
    from oauth_auth import get_auth_headers
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables
dotenv_path = find_dotenv()
if not dotenv_path:
    dotenv_path = '.env'

# Load environment variables directly from the .env file
env_vars = dotenv_values(dotenv_path)

# Set environment variables directly
for key, value in env_vars.items():
    # Remove quotes if present in the value
    if value and isinstance(value, str):
        # Strip quotes if they exist
        if (value.startswith("'") and value.endswith("'")) or (value.startswith('"') and value.endswith('"')):
            value = value[1:-1]
        os.environ[key] = value

# Now load with load_dotenv
load_dotenv(dotenv_path)

class LoginUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Databricks Genie - Authentication")
        self.root.geometry("650x750")
        self.root.minsize(600, 700)
        self.root.resizable(True, True)

        # Set background color
        self.root.configure(bg="#FFFFFF")

        # Set application icon if available
        try:
            self.root.iconbitmap("assets/favicon.ico")
        except:
            try:
                # Try using a PNG as icon
                icon = tk.PhotoImage(file="assets/databricks.png")
                self.root.iconphoto(True, icon)
            except:
                pass

        # Configure styles
        self.configure_styles()

        # Create main frame with padding
        self.main_frame = ttk.Frame(root, padding="20", style="Main.TFrame")
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Create header with logo
        self.create_header()

        # Create login notebook (tabbed interface)
        self.notebook = ttk.Notebook(self.main_frame, style="Auth.TNotebook")
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=10)

        # Create login tabs
        self.create_pat_login_tab()  # PAT authentication is more reliable

        # Always create the OAuth tab, even if OAuth is not available
        # This ensures both tabs are always present and can be switched between
        self.create_oauth_login_tab()

        # If OAuth is not available, add a warning message to the OAuth tab
        if not OAUTH_AVAILABLE:
            print("OAuth authentication is not available - adding warning message")
            oauth_tab = self.notebook.tabs()[1]  # Get the second tab (OAuth)
            oauth_frame = self.notebook.nametowidget(oauth_tab)

            # Add a warning message
            warning_frame = ttk.Frame(oauth_frame, padding=10)
            warning_frame.pack(fill=tk.BOTH, expand=True)

            warning_label = ttk.Label(
                warning_frame,
                text="OAuth authentication is not available.\n\nPlease install the required packages or use PAT authentication.",
                font=("Arial", 12, "bold"),
                foreground="red",
                justify=tk.CENTER
            )
            warning_label.pack(pady=50)

        # Create status bar
        self.status_var = tk.StringVar()
        status_frame = ttk.Frame(self.main_frame, style="StatusFrame.TFrame")
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(10, 0))

        status_bar = ttk.Label(
            status_frame,
            textvariable=self.status_var,
            style="Status.TLabel"
        )
        status_bar.pack(side=tk.LEFT, fill=tk.X, padx=5, pady=5)

        # Create footer with links
        self.create_footer()

        # Set initial status
        self.status_var.set("Ready to authenticate")

        # Store the previous authentication method
        self.previous_auth_type = os.getenv("CURRENT_AUTH_TYPE", "").lower()
        print(f"Previous authentication method: {self.previous_auth_type}")

        # Load saved settings after all UI elements are created
        self.load_saved_settings()

        # Add a callback when tabs are changed
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def configure_styles(self):
        """Configure custom styles for the UI with Databricks theme"""
        style = ttk.Style()

        # Set theme if available
        try:
            style.theme_use("clam")  # Use a more modern theme if available
        except:
            pass

        # Define Databricks colors
        bg_color = "#FFFFFF"
        databricks_red = "#FF3621"
        databricks_dark_red = "#DB1B0B"
        databricks_blue = "#2196F3"
        databricks_dark_blue = "#0D47A1"
        databricks_light_blue = "#E3F2FD"
        databricks_gray = "#F5F5F5"
        databricks_dark_gray = "#333333"
        databricks_light_gray = "#EEEEEE"
        databricks_text = "#212121"
        databricks_secondary_text = "#757575"
        databricks_border = "#DDDDDD"
        tudip_blue = "#0066CC"

        # Configure frame styles
        style.configure("Main.TFrame", background=bg_color)
        style.configure("StatusFrame.TFrame", background=databricks_light_gray, relief="flat")
        style.configure("Header.TFrame", background=bg_color)
        style.configure("Footer.TFrame", background=databricks_light_gray)

        # Configure label styles
        style.configure("Main.TLabel", background=bg_color)
        style.configure("Title.TLabel",
                       font=("Segoe UI", 18, "bold"),
                       background=bg_color,
                       foreground=databricks_text)
        style.configure("Subtitle.TLabel",
                       font=("Segoe UI", 11),
                       background=bg_color,
                       foreground=databricks_secondary_text)
        style.configure("Status.TLabel",
                       background=databricks_light_gray,
                       padding=5,
                       foreground=databricks_secondary_text)
        style.configure("Footer.TLabel",
                       background=databricks_light_gray,
                       foreground=databricks_secondary_text,
                       font=("Segoe UI", 9))

        # Configure button styles
        style.configure("Login.TButton",
                       font=("Segoe UI", 10, "bold"),
                       background=databricks_red,
                       foreground="white",
                       padding=(10, 5))
        style.map("Login.TButton",
                 background=[("active", databricks_dark_red), ("pressed", databricks_dark_red)],
                 foreground=[("active", "white"), ("pressed", "white")])

        style.configure("Secondary.TButton",
                       font=("Segoe UI", 10),
                       background=bg_color,
                       foreground=databricks_red,
                       padding=(10, 5))
        style.map("Secondary.TButton",
                 background=[("active", databricks_light_gray), ("pressed", databricks_light_gray)],
                 foreground=[("active", databricks_dark_red), ("pressed", databricks_dark_red)])

        # Configure notebook styles
        style.configure("Auth.TNotebook", background=bg_color)
        style.map("Auth.TNotebook.Tab",
                 background=[("selected", databricks_red), ("active", databricks_light_gray)],
                 foreground=[("selected", "white"), ("active", databricks_red)])

        # Configure entry styles
        style.configure("TEntry",
                       padding=8,
                       relief="flat",
                       background="#ffffff",
                       fieldbackground="#ffffff",
                       borderwidth=1)
        style.map("TEntry",
                 bordercolor=[("focus", databricks_red)])

        # Configure checkbutton styles
        style.configure("TCheckbutton", background=bg_color)
        style.map("TCheckbutton",
                 indicatorcolor=[("selected", databricks_red), ("active", databricks_light_gray)])

        # Configure labelframe styles
        style.configure("TLabelframe", background=bg_color)
        style.configure("TLabelframe.Label",
                       background=bg_color,
                       foreground=databricks_text,
                       font=("Segoe UI", 10, "bold"))

    def create_header(self):
        """Create the header with logos and title"""
        header_frame = ttk.Frame(self.main_frame, style="Header.TFrame")
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Create a frame for the logos
        logo_frame = ttk.Frame(header_frame, style="Header.TFrame")
        logo_frame.pack(fill=tk.X, pady=(0, 15))

        # Try to load and display Databricks logo
        databricks_logo = None
        try:
            # Try different possible logo files
            for logo_file in ["assets/databricks.png", "assets/databricks_logo.png"]:
                try:
                    databricks_logo = tk.PhotoImage(file=logo_file)
                    # Resize if needed
                    if databricks_logo.width() > 300:
                        ratio = databricks_logo.width() / 300
                        databricks_logo = databricks_logo.subsample(int(ratio), int(ratio))
                    break
                except:
                    continue
        except:
            databricks_logo = None

        # Try to load and display Tudip logo
        tudip_logo = None
        try:
            tudip_logo = tk.PhotoImage(file="assets/tudip.jpeg")
            # Resize if needed
            if tudip_logo and tudip_logo.width() > 150:
                ratio = tudip_logo.width() / 150
                tudip_logo = tudip_logo.subsample(int(ratio), int(ratio))
        except:
            try:
                # Try JPEG format
                from PIL import Image, ImageTk
                tudip_img = Image.open("assets/tudip.jpeg")
                # Resize to appropriate height
                width, height = tudip_img.size
                new_width = int(150 * width / height) if height > 0 else 150
                tudip_img = tudip_img.resize((new_width, 150), Image.LANCZOS)
                tudip_logo = ImageTk.PhotoImage(tudip_img)
            except:
                tudip_logo = None

        # Create a frame for the logos to be side by side
        logos_container = ttk.Frame(logo_frame, style="Header.TFrame")
        logos_container.pack(fill=tk.X)

        # Add Databricks logo on the left
        if databricks_logo:
            databricks_label = ttk.Label(logos_container, image=databricks_logo, style="Main.TLabel")
            databricks_label.image = databricks_logo  # Keep a reference
            databricks_label.pack(side=tk.LEFT, padx=(0, 20))
        else:
            # If logo can't be loaded, create a text-based logo
            databricks_text = ttk.Label(
                logos_container,
                text="DATABRICKS",
                font=("Segoe UI", 22, "bold"),
                foreground="#FF3621",
                style="Main.TLabel"
            )
            databricks_text.pack(side=tk.LEFT, padx=(0, 20))

        # Add Tudip logo on the right
        if tudip_logo:
            tudip_label = ttk.Label(logos_container, image=tudip_logo, style="Main.TLabel")
            tudip_label.image = tudip_logo  # Keep a reference
            tudip_label.pack(side=tk.RIGHT)
        else:
            # If logo can't be loaded, create a text-based logo
            tudip_text = ttk.Label(
                logos_container,
                text="TUDIP TECHNOLOGIES",
                font=("Segoe UI", 16, "bold"),
                foreground="#0066CC",
                style="Main.TLabel"
            )
            tudip_text.pack(side=tk.RIGHT)

        # Title
        title_label = ttk.Label(
            header_frame,
            text="Genie API Authentication",
            style="Title.TLabel"
        )
        title_label.pack(side=tk.TOP, pady=(15, 5))

        # Subtitle
        subtitle_label = ttk.Label(
            header_frame,
            text="Connect to Databricks Genie API and start conversations with your data",
            style="Subtitle.TLabel"
        )
        subtitle_label.pack(side=tk.TOP)

    def create_direct_login_tab(self):
        """Create the direct username/password login tab"""
        tab = ttk.Frame(self.notebook, padding=10)
        self.notebook.add(tab, text="Username / Password")

        # Databricks Host
        ttk.Label(tab, text="Databricks Host:").grid(column=0, row=0, sticky=tk.W, pady=5)
        self.host_var = tk.StringVar()
        host_entry = ttk.Entry(tab, textvariable=self.host_var, width=40)
        host_entry.grid(column=1, row=0, sticky=(tk.W, tk.E), pady=5)
        ttk.Label(tab, text="Example: dbc-123abc45-6def.cloud.databricks.com").grid(
            column=1, row=1, sticky=tk.W, pady=(0, 10))

        # Username
        ttk.Label(tab, text="Username:").grid(column=0, row=2, sticky=tk.W, pady=5)
        self.username_var = tk.StringVar()
        username_entry = ttk.Entry(tab, textvariable=self.username_var, width=40)
        username_entry.grid(column=1, row=2, sticky=(tk.W, tk.E), pady=5)

        # Password
        ttk.Label(tab, text="Password:").grid(column=0, row=3, sticky=tk.W, pady=5)
        self.password_var = tk.StringVar()
        password_entry = ttk.Entry(tab, textvariable=self.password_var, width=40, show="*")
        password_entry.grid(column=1, row=3, sticky=(tk.W, tk.E), pady=5)

        # Show/Hide password
        self.show_password_var = tk.BooleanVar(value=False)
        show_password_check = ttk.Checkbutton(
            tab, text="Show password",
            variable=self.show_password_var,
            command=lambda: password_entry.config(show="" if self.show_password_var.get() else "*"))
        show_password_check.grid(column=1, row=4, sticky=tk.W, pady=5)

        # Remember me
        self.remember_direct_var = tk.BooleanVar(value=False)
        remember_check = ttk.Checkbutton(
            tab, text="Remember me", variable=self.remember_direct_var)
        remember_check.grid(column=1, row=5, sticky=tk.W, pady=5)

        # Login button
        login_button = ttk.Button(
            tab, text="Login", command=self.direct_login)
        login_button.grid(column=1, row=6, sticky=tk.E, pady=10)

        # Help text
        help_frame = ttk.LabelFrame(tab, text="Help", padding=10)
        help_frame.grid(column=0, row=7, columnspan=2, sticky=(tk.W, tk.E), pady=10)

        help_text = ("Direct login uses your Databricks username and password.\n"
                    "This method is suitable for testing but not recommended for production use.\n"
                    "For better security, use Personal Access Token (PAT) or OAuth.")

        ttk.Label(help_frame, text=help_text, justify=tk.LEFT).pack(anchor=tk.W)

    def create_pat_login_tab(self):
        """Create the PAT login tab"""
        tab = ttk.Frame(self.notebook, padding=15)
        self.notebook.add(tab, text="Personal Access Token")

        # Create a frame for the form
        form_frame = ttk.Frame(tab)
        form_frame.pack(fill=tk.BOTH, expand=True)

        # Databricks Host
        host_label = ttk.Label(
            form_frame,
            text="Databricks Host:",
            font=("Arial", 10, "bold")
        )
        host_label.pack(anchor=tk.W, pady=(5, 2))

        self.pat_host_var = tk.StringVar()
        host_entry = ttk.Entry(form_frame, textvariable=self.pat_host_var, width=50)
        host_entry.pack(fill=tk.X, pady=(0, 5))

        host_example = ttk.Label(
            form_frame,
            text="Example: dbc-123abc45-6def.cloud.databricks.com",
            font=("Arial", 8)
        )
        host_example.pack(anchor=tk.W, pady=(0, 10))

        # Space ID
        space_label = ttk.Label(
            form_frame,
            text="Genie Space ID:",
            font=("Arial", 10, "bold")
        )
        space_label.pack(anchor=tk.W, pady=(5, 2))

        self.space_id_var = tk.StringVar()
        space_id_entry = ttk.Entry(form_frame, textvariable=self.space_id_var, width=50)
        space_id_entry.pack(fill=tk.X, pady=(0, 5))

        space_example = ttk.Label(
            form_frame,
            text="Example: 01f02f16a7b11b36a04e4353814a5699?o=1883526265026134",
            font=("Arial", 8)
        )
        space_example.pack(anchor=tk.W, pady=(0, 10))

        # PAT Token
        token_label = ttk.Label(
            form_frame,
            text="Access Token:",
            font=("Arial", 10, "bold")
        )
        token_label.pack(anchor=tk.W, pady=(5, 2))

        self.token_var = tk.StringVar()
        token_entry = ttk.Entry(form_frame, textvariable=self.token_var, width=50, show="*")
        token_entry.pack(fill=tk.X, pady=(0, 5))

        # Options frame
        options_frame = ttk.Frame(form_frame)
        options_frame.pack(fill=tk.X, pady=5)

        # Show/Hide token
        self.show_token_var = tk.BooleanVar(value=False)

        # Create a direct function that will be called immediately
        def toggle_token_visibility(*args):
            print(f"Toggle token visibility: {self.show_token_var.get()}")
            if self.show_token_var.get():
                token_entry.config(show="")
            else:
                token_entry.config(show="*")

        # Create the checkbutton with a direct command
        show_token_check = ttk.Checkbutton(
            options_frame,
            text="Show token",
            variable=self.show_token_var,
            command=toggle_token_visibility
        )

        # Manually call the function once to ensure it's properly initialized
        toggle_token_visibility()
        show_token_check.pack(side=tk.LEFT, padx=(0, 15))

        # Remember me
        self.remember_pat_var = tk.BooleanVar(value=True)  # Default to remember
        remember_check = ttk.Checkbutton(
            options_frame,
            text="Remember credentials",
            variable=self.remember_pat_var
        )
        remember_check.pack(side=tk.LEFT)

        # Button frame
        button_frame = ttk.Frame(form_frame)
        button_frame.pack(fill=tk.X, pady=15)

        # Generate token button
        generate_button = ttk.Button(
            button_frame,
            text="How to Get a Token",
            style="Login.TButton",
            command=self.show_token_help
        )
        generate_button.pack(side=tk.LEFT)

        # Login button
        login_button = ttk.Button(
            button_frame,
            text="Authenticate",
            style="Login.TButton",
            command=self.pat_login
        )
        login_button.pack(side=tk.RIGHT)

        # Help text
        help_frame = ttk.LabelFrame(form_frame, text="About Personal Access Tokens", padding=10)
        help_frame.pack(fill=tk.X, pady=10)

        help_text = (
            "Personal Access Tokens (PATs) provide secure authentication to the Databricks API.\n"
            "They are the recommended method for accessing the Genie API programmatically.\n\n"
            "Your token is stored securely and can be revoked at any time from your Databricks workspace."
        )

        ttk.Label(help_frame, text=help_text, justify=tk.LEFT).pack(anchor=tk.W)

    def show_token_help(self):
        """Show help for generating a token"""
        help_text = (
            "To generate a Personal Access Token:\n\n"
            "1. Log in to your Databricks workspace\n"
            "2. Click your username in the top-right corner\n"
            "3. Select 'User Settings'\n"
            "4. Go to the 'Access Tokens' tab\n"
            "5. Click 'Generate New Token'\n"
            "6. Provide a name and expiration, then click 'Generate'\n"
            "7. Copy the token and paste it in the Access Token field\n\n"
            "Note: The token will only be shown once when generated."
        )
        messagebox.showinfo("How to Generate a Token", help_text)

    def create_oauth_login_tab(self):
        """Create the OAuth login tab"""
        tab = ttk.Frame(self.notebook, padding=15)
        self.notebook.add(tab, text="OAuth")

        # Create a frame for the form
        form_frame = ttk.Frame(tab)
        form_frame.pack(fill=tk.BOTH, expand=True)

        # Initialize OAuth variables even if OAuth is not available
        # This ensures the tab can be displayed without errors
        if not hasattr(self, 'oauth_host_var'):
            self.oauth_host_var = tk.StringVar()
        if not hasattr(self, 'oauth_space_id_var'):
            self.oauth_space_id_var = tk.StringVar()
        if not hasattr(self, 'client_id_var'):
            self.client_id_var = tk.StringVar()
        if not hasattr(self, 'client_secret_var'):
            self.client_secret_var = tk.StringVar()
        if not hasattr(self, 'remember_oauth_var'):
            self.remember_oauth_var = tk.BooleanVar(value=True)

        # Databricks Host
        host_label = ttk.Label(
            form_frame,
            text="Databricks Host:",
            font=("Arial", 10, "bold")
        )
        host_label.pack(anchor=tk.W, pady=(5, 2))

        self.oauth_host_var = tk.StringVar()
        host_entry = ttk.Entry(form_frame, textvariable=self.oauth_host_var, width=50)
        host_entry.pack(fill=tk.X, pady=(0, 5))

        host_example = ttk.Label(
            form_frame,
            text="Example: dbc-123abc45-6def.cloud.databricks.com",
            font=("Arial", 8)
        )
        host_example.pack(anchor=tk.W, pady=(0, 10))

        # Space ID
        space_label = ttk.Label(
            form_frame,
            text="Genie Space ID:",
            font=("Arial", 10, "bold")
        )
        space_label.pack(anchor=tk.W, pady=(5, 2))

        self.oauth_space_id_var = tk.StringVar()
        space_id_entry = ttk.Entry(form_frame, textvariable=self.oauth_space_id_var, width=50)
        space_id_entry.pack(fill=tk.X, pady=(0, 5))

        space_example = ttk.Label(
            form_frame,
            text="Example: 01f02f16a7b11b36a04e4353814a5699?o=1883526265026134",
            font=("Arial", 8)
        )
        space_example.pack(anchor=tk.W, pady=(0, 10))

        # Client ID
        client_id_label = ttk.Label(
            form_frame,
            text="Client ID:",
            font=("Arial", 10, "bold")
        )
        client_id_label.pack(anchor=tk.W, pady=(5, 2))

        self.client_id_var = tk.StringVar()
        client_id_entry = ttk.Entry(form_frame, textvariable=self.client_id_var, width=50)
        client_id_entry.pack(fill=tk.X, pady=(0, 5))

        # Client Secret
        client_secret_label = ttk.Label(
            form_frame,
            text="Client Secret:",
            font=("Arial", 10, "bold")
        )
        client_secret_label.pack(anchor=tk.W, pady=(5, 2))

        self.client_secret_var = tk.StringVar()
        client_secret_entry = ttk.Entry(form_frame, textvariable=self.client_secret_var, width=50, show="*")
        client_secret_entry.pack(fill=tk.X, pady=(0, 5))

        # Options frame
        options_frame = ttk.Frame(form_frame)
        options_frame.pack(fill=tk.X, pady=5)

        # Show/Hide secret
        self.show_secret_var = tk.BooleanVar(value=False)

        # Create a direct function that will be called immediately
        def toggle_secret_visibility(*args):
            print(f"Toggle secret visibility: {self.show_secret_var.get()}")
            if self.show_secret_var.get():
                client_secret_entry.config(show="")
            else:
                client_secret_entry.config(show="*")

        # Create the checkbutton with a direct command
        show_secret_check = ttk.Checkbutton(
            options_frame,
            text="Show secret",
            variable=self.show_secret_var,
            command=toggle_secret_visibility
        )

        # Manually call the function once to ensure it's properly initialized
        toggle_secret_visibility()
        show_secret_check.pack(side=tk.LEFT, padx=(0, 15))

        # Remember me
        self.remember_oauth_var = tk.BooleanVar(value=True)  # Default to remember
        remember_check = ttk.Checkbutton(
            options_frame,
            text="Remember credentials",
            variable=self.remember_oauth_var
        )
        remember_check.pack(side=tk.LEFT)

        # Button frame
        button_frame = ttk.Frame(form_frame)
        button_frame.pack(fill=tk.X, pady=15)

        # OAuth info button
        oauth_info_button = ttk.Button(
            button_frame,
            text="OAuth Setup Guide",
            style="Login.TButton",
            command=self.show_oauth_help
        )
        oauth_info_button.pack(side=tk.LEFT)

        # Login button
        login_button = ttk.Button(
            button_frame,
            text="Authenticate",
            style="Login.TButton",
            command=self.oauth_login
        )
        login_button.pack(side=tk.RIGHT)

        # Help text
        help_frame = ttk.LabelFrame(form_frame, text="About OAuth Authentication", padding=10)
        help_frame.pack(fill=tk.X, pady=10)

        help_text = (
            "OAuth authentication provides a secure way to access the Databricks API using service principals.\n"
            "This method is recommended for automated processes and applications.\n\n"
            "Note: For the Genie API, your service principal must have at least 'Can View' permission\n"
            "on the Genie space to access all features."
        )

        ttk.Label(help_frame, text=help_text, justify=tk.LEFT).pack(anchor=tk.W)

    def show_oauth_help(self):
        """Show help for OAuth setup"""
        help_text = (
            "To set up OAuth authentication:\n\n"
            "1. Create a service principal in your Databricks workspace\n"
            "2. Generate an OAuth secret for the service principal\n"
            "3. Grant the service principal access to your Genie spaces\n"
            "4. Enter the Client ID and Client Secret in the form\n\n"
            "For detailed instructions, refer to the Databricks documentation:\n"
            "https://docs.databricks.com/dev-tools/api/latest/authentication.html"
        )
        messagebox.showinfo("OAuth Setup Guide", help_text)

    def create_footer(self):
        """Create footer with links and additional options"""
        # Create a separator
        separator = ttk.Separator(self.main_frame, orient="horizontal")
        separator.pack(fill=tk.X, pady=(20, 10))

        # Create footer frame
        footer_frame = ttk.Frame(self.main_frame, style="Footer.TFrame")
        footer_frame.pack(fill=tk.X, pady=(0, 0))

        # Create inner padding frame
        inner_frame = ttk.Frame(footer_frame, style="Footer.TFrame")
        inner_frame.pack(fill=tk.X, padx=10, pady=10)

        # Left side - Version and copyright info
        left_frame = ttk.Frame(inner_frame, style="Footer.TFrame")
        left_frame.pack(side=tk.LEFT)

        # Version info
        version_label = ttk.Label(
            left_frame,
            text="Databricks Genie Client v1.0",
            style="Footer.TLabel"
        )
        version_label.pack(side=tk.TOP, anchor=tk.W)

        # Copyright info
        copyright_label = ttk.Label(
            left_frame,
            text="© 2025 Tudip Technologies Pvt Ltd. All rights reserved.",
            style="Footer.TLabel"
        )
        copyright_label.pack(side=tk.TOP, anchor=tk.W)

        # Right side - Buttons
        button_frame = ttk.Frame(inner_frame, style="Footer.TFrame")
        button_frame.pack(side=tk.RIGHT)

        # Help button
        help_button = ttk.Button(
            button_frame,
            text="Help",
            style="Secondary.TButton",
            command=self.show_help
        )
        help_button.pack(side=tk.RIGHT, padx=5)

        # Documentation button
        docs_button = ttk.Button(
            button_frame,
            text="Documentation",
            style="Secondary.TButton",
            command=self.open_documentation
        )
        docs_button.pack(side=tk.RIGHT, padx=5)

        # Advanced settings button
        settings_button = ttk.Button(
            button_frame,
            text="Advanced Settings",
            style="Secondary.TButton",
            command=self.open_advanced_settings
        )
        settings_button.pack(side=tk.RIGHT, padx=5)

    def open_documentation(self):
        """Open the Databricks Genie API documentation"""
        import webbrowser
        webbrowser.open("https://docs.databricks.com/api/workspace/genie")

    def load_saved_settings(self):
        """Load saved settings from .env file"""
        # Reload environment variables to ensure we have the latest values
        from dotenv import load_dotenv, find_dotenv, dotenv_values
        dotenv_path = find_dotenv()

        # Force reload the .env file
        if dotenv_path:
            # Read the .env file directly
            env_vars = dotenv_values(dotenv_path)

            # Set environment variables directly
            for key, value in env_vars.items():
                # Remove quotes if present in the value
                if value and isinstance(value, str):
                    # Strip quotes if they exist
                    if (value.startswith("'") and value.endswith("'")) or (value.startswith('"') and value.endswith('"')):
                        value = value[1:-1]
                    value = value.strip()
                    os.environ[key] = value

            # Now load with load_dotenv to ensure all variables are set
            load_dotenv(dotenv_path, override=True)

        # Print debug information
        print("\n=== Loading saved settings from .env file ===")
        print(f"DATABRICKS_HOST: {os.getenv('DATABRICKS_HOST', '')}")
        print(f"DATABRICKS_SPACE_ID: {os.getenv('DATABRICKS_SPACE_ID', '')}")

        # Get the current authentication type
        current_auth_type = os.getenv("CURRENT_AUTH_TYPE", "").lower()
        print(f"CURRENT_AUTH_TYPE: {current_auth_type}")

        # Safely print token and client credentials
        token = os.getenv('DATABRICKS_TOKEN', '')
        if token:
            print(f"DATABRICKS_TOKEN: {token[:5]}...{token[-5:] if len(token) > 10 else ''}")
        else:
            print("DATABRICKS_TOKEN: <not set>")

        client_id = os.getenv('DATABRICKS_CLIENT_ID', '')
        if client_id:
            print(f"DATABRICKS_CLIENT_ID: {client_id[:5]}...{client_id[-5:] if len(client_id) > 10 else ''}")
        else:
            print("DATABRICKS_CLIENT_ID: <not set>")

        client_secret = os.getenv('DATABRICKS_CLIENT_SECRET', '')
        if client_secret:
            print(f"DATABRICKS_CLIENT_SECRET: {client_secret[:5]}...{client_secret[-5:] if len(client_secret) > 10 else ''}")
        else:
            print("DATABRICKS_CLIENT_SECRET: <not set>")

        # Direct login settings - only set if the attributes exist
        if hasattr(self, 'host_var'):
            self.host_var.set(os.getenv("DATABRICKS_HOST", "").strip())
        if hasattr(self, 'username_var'):
            self.username_var.set(os.getenv("DATABRICKS_USERNAME", "").strip())
        # Don't load password for security reasons

        # PAT settings
        if hasattr(self, 'pat_host_var'):
            self.pat_host_var.set(os.getenv("DATABRICKS_HOST", "").strip())
        if hasattr(self, 'space_id_var'):
            self.space_id_var.set(os.getenv("DATABRICKS_SPACE_ID", "").strip())
        if hasattr(self, 'token_var'):
            self.token_var.set(os.getenv("DATABRICKS_TOKEN", "").strip())

        # OAuth settings
        if OAUTH_AVAILABLE:
            if hasattr(self, 'oauth_host_var'):
                self.oauth_host_var.set(os.getenv("DATABRICKS_HOST", "").strip())
            if hasattr(self, 'oauth_space_id_var'):
                self.oauth_space_id_var.set(os.getenv("DATABRICKS_SPACE_ID", "").strip())
            if hasattr(self, 'client_id_var'):
                client_id = os.getenv("DATABRICKS_CLIENT_ID", "").strip()
                self.client_id_var.set(client_id)
                print(f"Setting client_id_var to: {client_id[:5]}...{client_id[-5:] if len(client_id) > 10 else ''}")
            if hasattr(self, 'client_secret_var'):
                client_secret = os.getenv("DATABRICKS_CLIENT_SECRET", "").strip()
                self.client_secret_var.set(client_secret)
                print(f"Setting client_secret_var to: {client_secret[:5]}...{client_secret[-5:] if len(client_secret) > 10 else ''}")

        # Only select a tab on initial load, not when switching tabs
        if hasattr(self, 'notebook') and not hasattr(self, 'initial_load_complete'):
            # Set a flag to indicate that initial load is complete
            self.initial_load_complete = True

            if current_auth_type == "oauth" and OAUTH_AVAILABLE and client_id and client_secret:
                # Select the OAuth tab (usually the second tab)
                print("Selecting OAuth tab based on current auth type and available credentials")
                self.notebook.select(1)
                self.status_var.set("Ready to authenticate with OAuth")
            elif current_auth_type == "pat" and token:
                # Select the PAT tab (usually the first tab)
                print("Selecting PAT tab based on current auth type and available token")
                self.notebook.select(0)
                self.status_var.set("Ready to authenticate with PAT")
            elif token and not (client_id and client_secret):
                # If we have a token but no OAuth credentials, select PAT tab
                print("Selecting PAT tab based on available token")
                self.notebook.select(0)
                self.status_var.set("Ready to authenticate with PAT")
            elif client_id and client_secret and OAUTH_AVAILABLE and not token:
                # If we have OAuth credentials but no token, select OAuth tab
                print("Selecting OAuth tab based on available credentials")
                self.notebook.select(1)
                self.status_var.set("Ready to authenticate with OAuth")
            else:
                # Default to PAT tab
                print("Defaulting to PAT tab")
                self.notebook.select(0)
                self.status_var.set("Ready to authenticate with PAT")

        print("=== Finished loading settings ===\n")

    def save_settings(self, auth_type):
        """Save settings to .env file based on authentication type"""
        # First, load existing values to preserve them
        from dotenv import load_dotenv, find_dotenv, dotenv_values
        dotenv_path = find_dotenv()
        existing_values = dotenv_values(dotenv_path) if dotenv_path else {}

        env_vars = {}

        if auth_type == "direct":
            if not self.remember_direct_var.get():
                return

            env_vars["DATABRICKS_HOST"] = self.host_var.get().strip()
            env_vars["DATABRICKS_USERNAME"] = self.username_var.get().strip()
            # Don't save password for security reasons

            # Preserve other credentials
            env_vars["DATABRICKS_TOKEN"] = existing_values.get("DATABRICKS_TOKEN", "")
            env_vars["DATABRICKS_CLIENT_ID"] = existing_values.get("DATABRICKS_CLIENT_ID", "")
            env_vars["DATABRICKS_CLIENT_SECRET"] = existing_values.get("DATABRICKS_CLIENT_SECRET", "")

        elif auth_type == "pat":
            if not self.remember_pat_var.get():
                return

            env_vars["DATABRICKS_HOST"] = self.pat_host_var.get().strip()
            env_vars["DATABRICKS_SPACE_ID"] = self.space_id_var.get().strip()
            env_vars["DATABRICKS_TOKEN"] = self.token_var.get().strip()

            # Store the current auth type
            env_vars["CURRENT_AUTH_TYPE"] = "pat"

            # Preserve OAuth credentials
            env_vars["DATABRICKS_CLIENT_ID"] = existing_values.get("DATABRICKS_CLIENT_ID", "")
            env_vars["DATABRICKS_CLIENT_SECRET"] = existing_values.get("DATABRICKS_CLIENT_SECRET", "")

        elif auth_type == "oauth":
            if not self.remember_oauth_var.get():
                return

            env_vars["DATABRICKS_HOST"] = self.oauth_host_var.get().strip()
            env_vars["DATABRICKS_SPACE_ID"] = self.oauth_space_id_var.get().strip()
            env_vars["DATABRICKS_CLIENT_ID"] = self.client_id_var.get().strip()
            env_vars["DATABRICKS_CLIENT_SECRET"] = self.client_secret_var.get().strip()

            # Store the current auth type
            env_vars["CURRENT_AUTH_TYPE"] = "oauth"

            # Preserve PAT credentials
            env_vars["DATABRICKS_TOKEN"] = existing_values.get("DATABRICKS_TOKEN", "")

        # Print debug information
        print(f"Saving settings for {auth_type} authentication")
        for key, value in env_vars.items():
            if key in ["DATABRICKS_TOKEN", "DATABRICKS_CLIENT_SECRET"]:
                if value:
                    print(f"{key}: {value[:5]}...{value[-5:] if len(value) > 10 else ''}")
                else:
                    print(f"{key}: <not set>")
            else:
                print(f"{key}: {value}")

        # Write to .env file
        for key, value in env_vars.items():
            # Ensure we're not adding quotes to the values
            set_key(dotenv_path, key, value, quote_mode="never")

    def direct_login(self):
        """Handle direct username/password login"""
        # Validate inputs
        if not self.host_var.get():
            messagebox.showerror("Error", "Databricks Host is required")
            return

        if not self.username_var.get() or not self.password_var.get():
            messagebox.showerror("Error", "Username and Password are required")
            return

        # Update status
        self.status_var.set("Logging in...")
        self.root.update()

        # Save settings if remember me is checked
        # Direct login settings are not saved for security reasons

        # TODO: Implement actual login logic
        # For now, just show a message
        messagebox.showinfo("Not Implemented",
                           "Direct login is not yet implemented.\n\n"
                           "Please use Personal Access Token (PAT) or OAuth authentication.")

        self.status_var.set("Login failed - Not implemented")

    def pat_login(self):
        """Handle PAT login"""
        # Validate inputs
        host = self.pat_host_var.get().strip()
        space_id = self.space_id_var.get().strip()
        token = self.token_var.get().strip()

        if not host:
            messagebox.showerror("Error", "Databricks Host is required")
            return

        if not space_id:
            messagebox.showerror("Error", "Genie Space ID is required")
            return

        if not token:
            messagebox.showerror("Error", "Access Token is required")
            return

        # Update the variables with stripped values
        self.pat_host_var.set(host)
        self.space_id_var.set(space_id)
        self.token_var.set(token)

        # Update status
        self.status_var.set("Logging in with PAT...")
        self.root.update()

        # Save settings if remember me is checked
        if self.remember_pat_var.get():
            self.save_pat_settings()

        # Test connection
        try:
            # Use the stripped values
            host = self.pat_host_var.get().strip()
            token = self.token_var.get().strip()

            # Print debug info
            print(f"PAT Login - Host: {host}")
            print(f"PAT Login - Token: {token[:5]}...{token[-5:] if len(token) > 10 else ''}")

            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

            # Test with a simple API call
            url = f"https://{host}/api/2.0/clusters/list"
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                # Now try the Genie API - use the full space ID including organization ID
                space_id_full = self.space_id_var.get()

                # Check if space_id is valid
                if not space_id_full:
                    messagebox.showinfo("Success", "Databricks API connection successful! Space ID validation skipped.")
                    self.status_var.set("Logged in successfully")
                    self.on_successful_login("pat")
                    return

                # Use the space ID directly without any prefix
                # Construct the URL with the correct format

                # Use the exact same approach as in the genie_api_explorer.py file
                # This is the approach that generated the log file with the 403 error

                # Print the original space ID for debugging
                print(f"Original Space ID: {space_id_full}")

                # Extract space ID and org ID if present
                space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full
                org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

                # Print the extracted space ID and org ID for debugging
                print(f"Extracted Space ID: {space_id}")
                print(f"Extracted Org ID: {org_id}")

                # Remove any 'datarooms/' prefix if it exists in the space_id
                if space_id.startswith('datarooms/'):
                    space_id = space_id.replace('datarooms/', '')
                    print(f"Space ID after removing 'datarooms/' prefix: {space_id}")

                # Use the web UI URL format
                if org_id:
                    genie_url = f"https://{host}/genie/rooms/{space_id}?o={org_id}"
                else:
                    genie_url = f"https://{host}/genie/rooms/{space_id}"

                # Print debug information
                print(f"Accessing Genie space with URL: {genie_url}")
                print(f"Using headers: {headers}")

                # Use GET instead of POST for the web UI URL
                genie_response = requests.get(genie_url, headers=headers)

                # Print response information
                print(f"Response status code: {genie_response.status_code}")
                print(f"Response text: {genie_response.text[:200]}")

                if genie_response.status_code == 200:
                    messagebox.showinfo("Success", "Authentication successful! You can now use the Genie API.")
                    self.status_var.set("Logged in successfully")
                    self.on_successful_login("pat")
                else:
                    if genie_response.status_code == 404:
                        error_message = (
                            "Authentication Error: Could not find Genie space\n\n"
                            f"The Space ID '{space_id_full}' could not be found.\n\n"
                            "Please verify:\n"
                            "• The Space ID is correct (format: 01f02f16a7b11b36a04e4353814a5699?o=1883526265026134)\n"
                            "• You can find this in the URL of your Genie space\n"
                            "• You have access to this Genie space"
                        )
                        messagebox.showerror("Authentication Error", error_message)
                    elif genie_response.status_code == 403:
                        # Extract the original error message from the API response
                        api_error = genie_response.text[:300] if genie_response.text else "No details available"

                        error_message = (
                            "Permission Denied\n\n"
                            "Your Personal Access Token (PAT) doesn't have access to this Genie space.\n\n"
                            "Please verify:\n"
                            "• Your PAT token has been granted access to this specific Genie space\n"
                            "• The Space ID is correct\n"
                            "• Your token has not expired\n"
                            "• You have the necessary permissions in Databricks\n\n"
                            "API Error Details:\n" + api_error
                        )
                        messagebox.showerror("Permission Denied", error_message)
                    else:
                        error_message = (
                            f"API Error (Status {genie_response.status_code})\n\n"
                            "Databricks API connection successful, but Genie API returned an unexpected error.\n\n"
                            "This may be due to:\n"
                            "• Permission issues\n"
                            "• API configuration problems\n"
                            "• Service unavailability\n\n"
                            f"Error details: {genie_response.text[:150] if genie_response.text else 'No details available'}"
                        )
                        messagebox.showerror("API Error", error_message)

                    self.status_var.set("Authentication failed")
            else:
                messagebox.showerror("Error", f"Login failed with status code {response.status_code}.\n\n{response.text}")
                self.status_var.set("Login failed")

        except Exception as e:
            messagebox.showerror("Error", f"Login failed: {str(e)}")
            self.status_var.set("Login failed")

    def oauth_login(self):
        """Handle OAuth login"""
        if not OAUTH_AVAILABLE:
            # Show a more helpful error message with instructions
            error_message = (
                "OAuth Authentication Not Available\n\n"
                "The required packages for OAuth authentication are not installed.\n\n"
                "To enable OAuth authentication, please install the required packages:\n"
                "1. Open a terminal or command prompt\n"
                "2. Navigate to the application directory\n"
                "3. Run: pip install requests-oauthlib msal\n\n"
                "After installation, restart the application."
            )
            messagebox.showerror("OAuth Not Available", error_message)
            return

        # Validate inputs
        host = self.oauth_host_var.get().strip()
        space_id = self.oauth_space_id_var.get().strip()
        client_id = self.client_id_var.get().strip()
        client_secret = self.client_secret_var.get().strip()

        # Print debug information
        print(f"OAuth Login - Host: {host}")
        print(f"OAuth Login - Space ID: {space_id}")

        # Safely print credentials
        if client_id:
            print(f"OAuth Login - Client ID: {client_id[:5]}...{client_id[-5:] if len(client_id) > 10 else ''}")
        else:
            print("OAuth Login - Client ID: <not set>")

        if client_secret:
            print(f"OAuth Login - Client Secret: {client_secret[:5]}...{client_secret[-5:] if len(client_secret) > 10 else ''}")
        else:
            print("OAuth Login - Client Secret: <not set>")

        if not host:
            messagebox.showerror("Error", "Databricks Host is required")
            return

        if not space_id:
            messagebox.showerror("Error", "Genie Space ID is required")
            return

        if not client_id or not client_secret:
            messagebox.showerror("Error", "Client ID and Client Secret are required")
            return

        # Update the variables with stripped values
        self.oauth_host_var.set(host)
        self.oauth_space_id_var.set(space_id)
        self.client_id_var.set(client_id)
        self.client_secret_var.set(client_secret)

        # Update status
        self.status_var.set("Logging in with OAuth...")
        self.root.update()

        # Save settings if remember me is checked
        if self.remember_oauth_var.get():
            self.save_oauth_settings()

        # Test connection
        try:
            # Set environment variables for OAuth module
            os.environ["DATABRICKS_HOST"] = self.oauth_host_var.get()
            os.environ["DATABRICKS_CLIENT_ID"] = self.client_id_var.get()
            os.environ["DATABRICKS_CLIENT_SECRET"] = self.client_secret_var.get()

            # Get OAuth headers
            from oauth_auth import get_auth_headers
            headers = get_auth_headers()

            # Test with a simple API call
            host = self.oauth_host_var.get()
            url = f"https://{host}/api/2.0/clusters/list"
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                # Now try the Genie API - use the full space ID including organization ID
                space_id_full = self.oauth_space_id_var.get()

                # Check if space_id is valid
                if not space_id_full:
                    messagebox.showinfo("Success", "Databricks API connection successful! Space ID validation skipped.")
                    self.status_var.set("Logged in successfully")
                    self.on_successful_login("oauth")
                    return

                # Use the space ID directly without any prefix
                # Construct the URL with the correct format

                # Use the exact same approach as in the genie_api_explorer.py file
                # This is the approach that generated the log file with the 403 error

                # Print the original space ID for debugging
                print(f"OAuth: Original Space ID: {space_id_full}")

                # Extract space ID and org ID if present
                space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full
                org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

                # Print the extracted space ID and org ID for debugging
                print(f"OAuth: Extracted Space ID: {space_id}")
                print(f"OAuth: Extracted Org ID: {org_id}")

                # Remove any 'datarooms/' prefix if it exists in the space_id
                if space_id.startswith('datarooms/'):
                    space_id = space_id.replace('datarooms/', '')
                    print(f"OAuth: Space ID after removing 'datarooms/' prefix: {space_id}")

                # Use the correct URL format for accessing the Genie space
                try:
                    # Import the OAuth URL functions
                    from oauth_auth import get_genie_space_url

                    # Get the correct URL for accessing the Genie space
                    genie_url = get_genie_space_url(space_id)
                    print(f"OAuth: Using correct URL format: {genie_url}")
                except ImportError:
                    # Fall back to the old method if the OAuth URL functions are not available
                    if org_id:
                        genie_url = f"https://{host}/api/2.0/genie/spaces/{space_id}?o={org_id}"
                    else:
                        genie_url = f"https://{host}/api/2.0/genie/spaces/{space_id}"
                    print(f"OAuth: Using fallback URL format: {genie_url}")

                # Print debug information
                print(f"OAuth: Accessing Genie space with URL: {genie_url}")
                print(f"OAuth: Using headers: {headers}")

                # Use GET for the spaces endpoint
                genie_response = requests.get(genie_url, headers=headers)

                # Print response information
                print(f"OAuth: Response status code: {genie_response.status_code}")
                print(f"OAuth: Response text: {genie_response.text[:200]}")

                if genie_response.status_code == 200:
                    messagebox.showinfo("Success", "Authentication successful! You can now use the Genie API.")
                    self.status_var.set("Logged in successfully")
                    self.on_successful_login("oauth")
                else:
                    if genie_response.status_code == 404:
                        error_message = (
                            "Authentication Error: Could not find Genie space\n\n"
                            f"The Space ID '{space_id_full}' could not be found.\n\n"
                            "Please verify:\n"
                            "• The Space ID is correct (format: 01f02f16a7b11b36a04e4353814a5699?o=1883526265026134)\n"
                            "• You can find this in the URL of your Genie space\n\n"
                            "For OAuth authentication, ensure:\n"
                            "• Your service principal has access to this Genie space\n"
                            "• Consider using PAT authentication if OAuth continues to fail"
                        )
                        messagebox.showerror("Authentication Error", error_message)
                    elif genie_response.status_code == 403:
                        # Extract the original error message from the API response
                        api_error = genie_response.text[:300] if genie_response.text else "No details available"

                        # Check if this is a permission error
                        if "PERMISSION_DENIED" in api_error:
                            error_message = (
                                "Authentication Successful with Warning\n\n"
                                "Your OAuth credentials authenticated successfully, but have limited permissions.\n\n"
                                "You may encounter permission restrictions when using certain features.\n\n"
                                "API Error Details:\n" + api_error
                            )
                            # For OAuth with permission warnings, we can still proceed
                            messagebox.showinfo("Authentication Status", error_message)
                            self.status_var.set("OAuth authentication successful with permission warnings")
                            self.on_successful_login("oauth")
                        else:
                            # For other 403 errors, we can still proceed but with a different message
                            error_message = (
                                "Authentication Successful\n\n"
                                "Your OAuth credentials authenticated successfully.\n\n"
                                "However, there was a non-critical error that you can ignore:\n" + api_error
                            )
                            messagebox.showinfo("Authentication Status", error_message)
                            self.status_var.set("OAuth authentication successful")
                            self.on_successful_login("oauth")
                    else:
                        error_message = (
                            f"API Error (Status {genie_response.status_code})\n\n"
                            "Databricks API connection successful, but Genie API returned an unexpected error.\n\n"
                            "This may be due to:\n"
                            "• Permission issues\n"
                            "• API configuration problems\n"
                            "• Service unavailability\n\n"
                            f"Error details: {genie_response.text[:150] if genie_response.text else 'No details available'}"
                        )
                        messagebox.showerror("API Error", error_message)
                        self.status_var.set("Authentication failed")
            else:
                messagebox.showerror("Error", f"Login failed with status code {response.status_code}.\n\n{response.text}")
                self.status_var.set("Login failed")

        except Exception as e:
            messagebox.showerror("Error", f"Login failed: {str(e)}")
            self.status_var.set("Login failed")

    def save_pat_settings(self):
        """Save PAT settings to .env file"""
        # First, load existing values to preserve them
        from dotenv import load_dotenv, find_dotenv, dotenv_values
        dotenv_path = find_dotenv()
        existing_values = dotenv_values(dotenv_path) if dotenv_path else {}

        # Prepare new values, keeping existing OAuth credentials
        env_vars = {
            "DATABRICKS_HOST": self.pat_host_var.get().strip(),
            "DATABRICKS_SPACE_ID": self.space_id_var.get().strip(),
            "DATABRICKS_TOKEN": self.token_var.get().strip(),
            # Set the current auth type
            "CURRENT_AUTH_TYPE": "pat",
            # Preserve existing OAuth credentials
            "DATABRICKS_CLIENT_ID": existing_values.get("DATABRICKS_CLIENT_ID", ""),
            "DATABRICKS_CLIENT_SECRET": existing_values.get("DATABRICKS_CLIENT_SECRET", "")
        }

        # Print debug information
        print("Saving PAT settings to .env file")
        for key, value in env_vars.items():
            if key in ["DATABRICKS_TOKEN", "DATABRICKS_CLIENT_SECRET"]:
                if value:
                    print(f"{key}: {value[:5]}...{value[-5:] if len(value) > 10 else ''}")
                else:
                    print(f"{key}: <not set>")
            else:
                print(f"{key}: {value}")

        # Write to .env file
        for key, value in env_vars.items():
            # Ensure we're not adding quotes to the values
            set_key(dotenv_path, key, value, quote_mode="never")

    def save_oauth_settings(self):
        """Save OAuth settings to .env file"""
        # First, load existing values to preserve them
        from dotenv import load_dotenv, find_dotenv, dotenv_values
        dotenv_path = find_dotenv()
        existing_values = dotenv_values(dotenv_path) if dotenv_path else {}

        # Prepare new values, keeping existing PAT credentials
        env_vars = {
            "DATABRICKS_HOST": self.oauth_host_var.get().strip(),
            "DATABRICKS_SPACE_ID": self.oauth_space_id_var.get().strip(),
            "DATABRICKS_CLIENT_ID": self.client_id_var.get().strip(),
            "DATABRICKS_CLIENT_SECRET": self.client_secret_var.get().strip(),
            # Set the current auth type
            "CURRENT_AUTH_TYPE": "oauth",
            # Preserve existing PAT credentials
            "DATABRICKS_TOKEN": existing_values.get("DATABRICKS_TOKEN", "")
        }

        # Print debug information
        print("Saving OAuth settings to .env file")
        for key, value in env_vars.items():
            if key in ["DATABRICKS_TOKEN", "DATABRICKS_CLIENT_SECRET"]:
                if value:
                    print(f"{key}: {value[:5]}...{value[-5:] if len(value) > 10 else ''}")
                else:
                    print(f"{key}: <not set>")
            else:
                print(f"{key}: {value}")

        # Write to .env file
        for key, value in env_vars.items():
            # Ensure we're not adding quotes to the values
            set_key(dotenv_path, key, value, quote_mode="never")

    def on_successful_login(self, auth_type):
        """Handle successful login"""
        # Save settings if remember me is checked
        if auth_type == "pat" and self.remember_pat_var.get():
            self.save_pat_settings()
        elif auth_type == "oauth" and self.remember_oauth_var.get():
            self.save_oauth_settings()

        # Launch the main application
        try:
            # Import the main application
            from genie_app import GenieApp

            # Store the authentication type in an environment variable
            # This will be used by the Genie app to display the current auth method
            os.environ["CURRENT_AUTH_TYPE"] = auth_type

            # Close the login window
            self.root.destroy()

            # Create a new root window for the main application
            root = tk.Tk()
            root.title(f"Databricks Genie - {auth_type.upper()} Authentication")
            root.geometry("800x600")  # Set a default size

            # Initialize the main application
            app = GenieApp(root, auth_type=auth_type)

            # Start the main loop
            root.mainloop()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch main application: {str(e)}")
            print(f"Error details: {str(e)}")
            # Can't show login window again since we destroyed it

    def open_advanced_settings(self):
        """Open the advanced settings dialog"""
        # For now, just open the existing auth_ui.py if it exists
        try:
            if os.path.exists("auth_ui.py"):
                # Run the auth_ui.py script
                import subprocess
                subprocess.Popen([sys.executable, "auth_ui.py"])
            else:
                messagebox.showinfo("Info", "Advanced settings are not available")
        except Exception as e:
            messagebox.showerror("Error", f"Could not open advanced settings: {str(e)}")

    # Flag to prevent infinite loops when changing tabs
    _tab_change_in_progress = False

    def on_tab_changed(self, event=None):
        """Handle tab change events to dynamically update authentication method"""
        # Prevent infinite loops when changing tabs
        if LoginUI._tab_change_in_progress:
            return

        LoginUI._tab_change_in_progress = True

        try:
            selected_tab = self.notebook.index(self.notebook.select())
            print(f"Tab changed to index: {selected_tab}")

            # Determine the authentication type based on the selected tab
            if selected_tab == 0:  # PAT tab
                auth_type = "pat"
            elif selected_tab == 1:  # OAuth tab
                auth_type = "oauth"
            else:
                auth_type = "unknown"

            print(f"Authentication type changed to: {auth_type}")

            # Update the current authentication type in the environment
            os.environ["CURRENT_AUTH_TYPE"] = auth_type

            # If we have credentials for this auth type, update the .env file
            if auth_type == "pat" and hasattr(self, 'token_var') and self.token_var.get().strip():
                print("Automatically saving PAT settings due to tab change")
                self.save_pat_settings()
            elif auth_type == "oauth" and hasattr(self, 'client_id_var') and self.client_id_var.get().strip():
                print("Automatically saving OAuth settings due to tab change")
                self.save_oauth_settings()

            # Update the status bar
            if auth_type == "oauth" and not OAUTH_AVAILABLE:
                self.status_var.set("OAuth authentication is not available")
            else:
                self.status_var.set(f"Ready to authenticate with {auth_type.upper()}")

            # Update the previous authentication type
            if hasattr(self, 'previous_auth_type') and self.previous_auth_type != auth_type:
                print(f"Authentication type changed from {self.previous_auth_type} to {auth_type}")
                self.previous_auth_type = auth_type
        finally:
            # Always reset the flag when done
            LoginUI._tab_change_in_progress = False

    def show_help(self):
        """Show help information"""
        help_text = """
Databricks Genie API Client

This application allows you to connect to the Databricks Genie API and have conversations with your data using natural language.

Authentication Methods:

• Personal Access Token (PAT): Recommended for most users
  - Simple to set up and use
  - Tokens can be generated from your Databricks workspace
  - Secure and can be revoked if needed

• OAuth: For service accounts and automated processes
  - Uses service principals for authentication
  - Requires additional setup in your Databricks workspace
  - Best for programmatic access

After authentication, you'll be able to:
• Start new conversations with Genie
• Ask questions about your data in natural language
• View SQL queries generated by Genie
• See query results and visualizations

For more information, visit:
https://docs.databricks.com/api/workspace/genie
"""
        messagebox.showinfo("Databricks Genie API Client", help_text)

def main():
    """Main function"""
    root = tk.Tk()
    LoginUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
