# Databricks Genie API OAuth Authentication Solution

This document explains the solution for using OAuth authentication with the Databricks Genie API in the UI application.

## The Problem

When using OAuth authentication with the Databricks Genie API, we encountered a 403 "PERMISSION_DENIED" error with the message "You need 'Can View' permission to perform this action". This happened even after granting the service principal the necessary permissions on the Genie space.

The root cause of this issue was using incorrect API URL formats. The Databricks Genie API requires specific URL formats for different operations.

## The Solution

We've implemented a solution that uses the correct API URL formats for OAuth authentication with the Databricks Genie API. The key components of the solution are:

1. **Correct API URL Formats**:
   - For accessing a specific Genie space: 
     ```
     https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}?o={org_id}
     ```
   - For starting a conversation:
     ```
     https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation?o={org_id}
     ```
   - For getting a message:
     ```
     https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}?o={org_id}
     ```
   - For sending a message to a conversation:
     ```
     https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages?o={org_id}
     ```

2. **Helper Functions in oauth_auth.py**:
   - `get_genie_space_url()`: Returns the URL for accessing a specific Genie space
   - `get_start_conversation_url()`: Returns the URL for starting a conversation
   - `get_message_url()`: Returns the URL for getting a message
   - `get_send_message_url()`: Returns the URL for sending a message to a conversation

3. **Integration with the UI**:
   - The `genie_app.py` file has been updated to use these helper functions
   - The OAuth authentication now works correctly with the Genie API
   - The UI displays the correct authentication status and error messages

## Files Modified

1. **oauth_auth.py**: Added helper functions for generating the correct API URLs
2. **genie_app.py**: Updated to use the helper functions for OAuth authentication

## Testing

The solution has been tested with both PAT and OAuth authentication, and it works correctly with both methods. The UI now displays the correct authentication status and error messages, and it can successfully:

1. Access the Genie space
2. Start a conversation
3. Send messages to a conversation
4. Receive responses from the Genie API

## Usage

To use the solution, simply run the application as usual:

```bash
python main.py
```

The application will automatically use the correct API URL formats for OAuth authentication.

## Troubleshooting

If you encounter any issues with OAuth authentication, check the following:

1. **Service Principal Permissions**:
   - Make sure the service principal has "Can View" permission on the Genie space
   - Make sure the service principal is a member of the necessary groups (admins, users, account users)

2. **OAuth Credentials**:
   - Make sure the client ID and client secret are correct
   - Make sure the client ID includes the correct prefix (e.g., "9e4f6f53-e2fb-4f7c-a7ce-bf1889db9bdc")

3. **Space ID Format**:
   - Make sure the space ID is in the correct format (e.g., "01f02f16a7b11b36a04e4353814a5699?o=****************")
   - The application will automatically extract the space ID and organization ID from this format

## Conclusion

The solution provides a reliable way to use OAuth authentication with the Databricks Genie API in the UI application. By using the correct API URL formats and helper functions, we've eliminated the 403 "PERMISSION_DENIED" error and enabled seamless integration with the Genie API.
