#!/usr/bin/env python3
"""
Databricks Genie Web Application Launcher

This script provides a menu to launch different versions of the Genie web application.
"""

import os
import sys
import subprocess
import time
import signal
import platform

# Define colors for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def clear_screen():
    """Clear the terminal screen"""
    os.system('cls' if platform.system() == 'Windows' else 'clear')

def print_header():
    """Print the application header"""
    clear_screen()
    print(f"{Colors.HEADER}{Colors.BOLD}")
    print("Databricks Genie Web Application Launcher")
    print("=========================================")
    print(f"{Colors.ENDC}")
    print("Databricks Genie Web Application Launcher")
    print("=========================================")
    print()

def print_menu():
    """Print the menu options"""
    print(f"{Colors.BLUE}Select a version to run:{Colors.ENDC}")
    print(f"{Colors.GREEN}1. Version 1: Web App with OAuth Login Screen{Colors.ENDC}")
    print(f"{Colors.GREEN}2. Version 2: Web App with Config File (No Login Screen){Colors.ENDC}")
    print(f"{Colors.GREEN}3. Version 3: Authentication Only Web Component{Colors.ENDC}")
    print(f"{Colors.GREEN}4. Original Web App (with PAT and OAuth){Colors.ENDC}")
    print(f"{Colors.RED}0. Exit{Colors.ENDC}")
    print()

def run_app(app_path, app_name):
    """Run the selected web application"""
    print(f"\nStarting {app_name}...")
    
    # Determine the correct Python command based on the environment
    python_cmd = "python"
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        # We're in a virtual environment, use the current Python
        python_cmd = sys.executable
    
    # Construct the command
    if app_path == "web_app":
        cmd = [python_cmd, os.path.join(app_path, "app.py")]
    elif app_path == "web_app_v1":
        cmd = [python_cmd, os.path.join(app_path, "run_app.py")]
    elif app_path == "web_app_v2":
        cmd = [python_cmd, os.path.join(app_path, "run_app.py")]
    elif app_path == "web_app_v3":
        cmd = [python_cmd, os.path.join(app_path, "run_demo.py")]
    
    # Start the process
    try:
        process = subprocess.Popen(cmd)
        print("Web application is running at http://localhost:5000")
        print("Press Ctrl+C to stop the server.")
        
        # Wait for the process to complete or for user to interrupt
        process.wait()
    except KeyboardInterrupt:
        print("\nStopping the server...")
        process.send_signal(signal.SIGINT)
        time.sleep(1)
        if process.poll() is None:
            process.terminate()
        print("Server stopped.")
    except Exception as e:
        print(f"Error running the application: {str(e)}")

def main():
    """Main function"""
    while True:
        print_header()
        print_menu()
        
        try:
            choice = input("Enter your choice (0-4): ")
            
            if choice == "0":
                print("Exiting...")
                break
            elif choice == "1":
                run_app("web_app_v1", "Version 1: Web App with OAuth Login Screen")
            elif choice == "2":
                run_app("web_app_v2", "Version 2: Web App with Config File (No Login Screen)")
            elif choice == "3":
                run_app("web_app_v3", "Version 3: Authentication Only Web Component")
            elif choice == "4":
                run_app("web_app", "Original Web App (with PAT and OAuth)")
            else:
                print(f"{Colors.RED}Invalid choice. Please try again.{Colors.ENDC}")
                time.sleep(2)
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"{Colors.RED}Error: {str(e)}{Colors.ENDC}")
            time.sleep(2)

if __name__ == "__main__":
    main()
