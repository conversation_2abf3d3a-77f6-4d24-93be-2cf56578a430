#!/usr/bin/env python3
"""
Databricks Genie Command-Line Interface

This script provides a command-line interface for interacting with the Databricks Genie API.
It can be used on systems without tkinter or as an alternative to the GUI.

Usage:
    python genie_cli.py [question]
"""

import os
import sys
import json
import time

# Try to import required packages
try:
    import requests
    from dotenv import load_dotenv
    REQUIRED_PACKAGES_AVAILABLE = True
except ImportError:
    REQUIRED_PACKAGES_AVAILABLE = False
    print("Error: Required packages are not installed.")
    print("To install required packages:")
    print("  pip install python-dotenv requests")
    print("\nAlternatively, you can install all requirements:")
    print("  pip install -r requirements.txt")
    sys.exit(1)

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")

# Check if we have the required configuration
if not DATABRICKS_HOST:
    print("Error: DATABRICKS_HOST is not set.")
    print("Please set the DATABRICKS_HOST environment variable or add it to your .env file.")
    sys.exit(1)

if not DATABRICKS_SPACE_ID:
    print("Error: DATABRICKS_SPACE_ID is not set.")
    print("Please set the DATABRICKS_SPACE_ID environment variable or add it to your .env file.")
    sys.exit(1)

# Check authentication method
USE_OAUTH = DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET
if not USE_OAUTH and not DATABRICKS_TOKEN:
    print("Error: No authentication method available.")
    print("Please provide either a personal access token (DATABRICKS_TOKEN) or OAuth credentials (DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET).")
    sys.exit(1)

# API Base URL
# Extract the space ID without any query parameters
SPACE_ID = DATABRICKS_SPACE_ID.split('?')[0] if '?' in DATABRICKS_SPACE_ID else DATABRICKS_SPACE_ID
ORG_ID = None

# Check if there's an organization ID in the space ID
if '?' in DATABRICKS_SPACE_ID and 'o=' in DATABRICKS_SPACE_ID:
    ORG_ID = DATABRICKS_SPACE_ID.split('o=')[1] if 'o=' in DATABRICKS_SPACE_ID else None

# Construct the base URL without the endpoint
BASE_URL = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{SPACE_ID}"

# Headers for API requests
if USE_OAUTH:
    try:
        from oauth_auth import get_auth_headers
        HEADERS = get_auth_headers()
        print("Using OAuth authentication")
    except ImportError:
        print("Error: OAuth authentication module not found.")
        print("Please install the oauth_auth.py module or use PAT authentication.")
        sys.exit(1)
else:
    HEADERS = {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }
    print("Using Personal Access Token (PAT) authentication")

def start_conversation(question):
    """Start a new conversation with Genie"""
    # Construct the URL with the endpoint and query parameter if needed
    endpoint = "/start-conversation"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    payload = {"content": question}

    print(f"Starting conversation with question: '{question}'")
    response = requests.post(url, headers=HEADERS, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error starting conversation: {response.text}")
        return None

def wait_for_message_completion(conversation_id, message_id):
    """Wait for a message to complete and get the response"""
    # Construct the URL
    endpoint = f"/conversations/{conversation_id}/messages/{message_id}"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    print("Waiting for response...")

    # Poll for message completion
    max_attempts = 30
    for attempt in range(max_attempts):
        response = requests.get(url, headers=HEADERS)

        if response.status_code == 200:
            data = response.json()
            status = data.get("status")

            if status == "COMPLETED":
                return data

            elif status == "FAILED":
                error_msg = data.get("error_message", "Unknown error")
                print(f"Error: {error_msg}")
                return None

        # Wait before polling again
        time.sleep(1)
        sys.stdout.write(".")
        sys.stdout.flush()

    # If we get here, the message didn't complete in time
    print("\nResponse timed out")
    return None

def send_follow_up(conversation_id, question):
    """Send a follow-up question to an existing conversation"""
    # Construct the URL
    endpoint = f"/conversations/{conversation_id}/messages"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    payload = {"content": question}

    print(f"Sending follow-up question: '{question}'")
    response = requests.post(url, headers=HEADERS, json=payload)

    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error sending follow-up: {response.text}")
        return None

def interactive_mode():
    """Run in interactive mode, allowing multiple questions"""
    print("Databricks Genie CLI - Interactive Mode")
    print("Type 'exit' or 'quit' to end the session")
    print("Type 'new' to start a new conversation")
    print()

    conversation_id = None

    while True:
        # Get user input
        question = input("> ")

        # Check for exit commands
        if question.lower() in ["exit", "quit"]:
            break

        # Check for new conversation command
        if question.lower() == "new":
            conversation_id = None
            print("Starting a new conversation")
            continue

        # Skip empty questions
        if not question.strip():
            continue

        # Start a new conversation or send a follow-up
        if not conversation_id:
            result = start_conversation(question)
            if not result:
                continue

            conversation_id = result.get("conversation_id")
            message_id = result.get("message_id")
        else:
            result = send_follow_up(conversation_id, question)
            if not result:
                continue

            message_id = result.get("message_id")

        # Wait for the response
        message = wait_for_message_completion(conversation_id, message_id)
        if message:
            print("\n" + message.get("content", "No response content"))

            # Check for attachments
            attachments = message.get("attachments", [])
            if attachments:
                print("\nAttachments:")
                for attachment in attachments:
                    print(f"- {attachment.get('type')}: {attachment.get('name', 'Unnamed')}")

        print()

def main():
    """Main function"""
    # Check if a question was provided as a command-line argument
    if len(sys.argv) > 1:
        # Use the command-line argument as the question
        question = " ".join(sys.argv[1:])

        # Start a conversation
        result = start_conversation(question)
        if not result:
            sys.exit(1)

        conversation_id = result.get("conversation_id")
        message_id = result.get("message_id")

        # Wait for the response
        message = wait_for_message_completion(conversation_id, message_id)
        if message:
            print("\n" + message.get("content", "No response content"))

            # Check for attachments
            attachments = message.get("attachments", [])
            if attachments:
                print("\nAttachments:")
                for attachment in attachments:
                    print(f"- {attachment.get('type')}: {attachment.get('name', 'Unnamed')}")
    else:
        # No question provided, run in interactive mode
        interactive_mode()

if __name__ == "__main__":
    main()
