#!/usr/bin/env python3
"""
Databricks Genie API Client

This is the main entry point for the Databricks Genie API client application.
It provides a user-friendly interface for interacting with the Databricks Genie API.

Usage:
    python main.py
"""

import os
import sys
import tkinter as tk
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_dependencies():
    """Check if all required dependencies are installed"""
    missing_deps = []

    try:
        import requests
    except ImportError:
        missing_deps.append("requests")

    try:
        from dotenv import load_dotenv
    except ImportError:
        missing_deps.append("python-dotenv")

    try:
        import tkinter as tk
    except ImportError:
        missing_deps.append("tkinter")

    return missing_deps

def install_dependencies(missing_deps):
    """Prompt the user to install missing dependencies"""
    if not missing_deps:
        return True

    print("The following dependencies are missing:")
    for dep in missing_deps:
        print(f"  - {dep}")

    try:
        choice = input("\nWould you like to install them now? (y/n): ").strip().lower()
        if choice == 'y':
            import subprocess

            # Install Python packages
            python_deps = [dep for dep in missing_deps if dep != "tkinter"]
            if python_deps:
                print("\nInstalling Python packages...")
                subprocess.check_call([sys.executable, "-m", "pip", "install"] + python_deps)

            # Provide instructions for tkinter if needed
            if "tkinter" in missing_deps:
                print("\ntkinter needs to be installed separately:")
                print("  - On Ubuntu/Debian: sudo apt-get install python3-tk")
                print("  - On Fedora/RHEL: sudo dnf install python3-tkinter")
                print("  - On macOS: brew install python-tk")
                print("  - On Windows: tkinter is included with Python by default")
                return False

            return True
        else:
            return False
    except Exception as e:
        print(f"Error installing dependencies: {str(e)}")
        return False

def main():
    """Main function"""
    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        if not install_dependencies(missing_deps):
            print("\nCannot continue without required dependencies.")
            print("Please install them manually and try again.")
            return

    # Check if authentication is configured
    host = os.getenv("DATABRICKS_HOST")
    token = os.getenv("DATABRICKS_TOKEN")
    space_id = os.getenv("DATABRICKS_SPACE_ID")
    client_id = os.getenv("DATABRICKS_CLIENT_ID")
    client_secret = os.getenv("DATABRICKS_CLIENT_SECRET")

    # If authentication is not configured, launch the login UI
    if not (host and (token or (client_id and client_secret)) and space_id):
        print("Authentication not configured. Launching login UI...")
        try:
            from login_ui import main as login_main
            login_main()
            return
        except ImportError:
            print("Error: Could not import login_ui.py")
            print("Please make sure the file exists in the current directory.")
            return

    # Even if authentication is configured, always show the login UI first
    try:
        from login_ui import main as login_main
        login_main()
    except ImportError:
        print("Error: Could not import login_ui.py")
        print("Please make sure the file exists in the current directory.")

if __name__ == "__main__":
    main()
