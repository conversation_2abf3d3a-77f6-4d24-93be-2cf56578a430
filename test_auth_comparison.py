#!/usr/bin/env python3
"""
Compare OAuth and PAT Authentication for Databricks Genie API

This script tests both OAuth and PAT authentication methods with the Genie API
to understand why OAuth is failing while PAT works.
"""

import os
import json
import requests
from dotenv import load_dotenv

# Import OAuth authentication module
try:
    from oauth_auth import get_auth_headers as get_oauth_headers
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")

# Check if required environment variables are set
if not DATABRICKS_HOST or not DATABRICKS_SPACE_ID:
    print("Error: Missing required environment variables.")
    print("Please set DATABRICKS_HOST and DATABRICKS_SPACE_ID in your .env file.")
    exit(1)

# Check authentication methods availability
if not DATABRICKS_TOKEN:
    print("Warning: PAT authentication not available.")
    print("Please set DATABRICKS_TOKEN in your .env file to test PAT authentication.")

if not OAUTH_AVAILABLE or not DATABRICKS_CLIENT_ID or not DATABRICKS_CLIENT_SECRET:
    print("Warning: OAuth authentication not available.")
    print("Please set DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET in your .env file to test OAuth authentication.")

# Extract the space ID without any query parameters
SPACE_ID = DATABRICKS_SPACE_ID.split('?')[0] if '?' in DATABRICKS_SPACE_ID else DATABRICKS_SPACE_ID
ORG_ID = None

# Check if there's an organization ID in the space ID
if '?' in DATABRICKS_SPACE_ID and 'o=' in DATABRICKS_SPACE_ID:
    ORG_ID = DATABRICKS_SPACE_ID.split('o=')[1] if 'o=' in DATABRICKS_SPACE_ID else None

# Remove any 'datarooms/' prefix if it exists in the SPACE_ID
if SPACE_ID.startswith('datarooms/'):
    SPACE_ID = SPACE_ID.replace('datarooms/', '')

def test_with_pat():
    """Test Genie API with Personal Access Token (PAT)"""
    if not DATABRICKS_TOKEN:
        print("Skipping PAT authentication test (no token available)")
        return
    
    print("\n=== Testing with Personal Access Token (PAT) ===")
    
    # Headers for API requests
    headers = {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }
    
    # Test start-conversation endpoint
    endpoint = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{SPACE_ID}/start-conversation"
    if ORG_ID:
        endpoint = f"{endpoint}?o={ORG_ID}"
    
    payload = {"content": "Test message from PAT authentication"}
    
    print(f"POST {endpoint}")
    print(f"Headers: {json.dumps({k: '***' if k == 'Authorization' else v for k, v in headers.items()}, indent=2)}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(endpoint, headers=headers, json=payload)
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("PAT authentication successful!")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"PAT authentication failed with status code {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error with PAT authentication: {str(e)}")

def test_with_oauth():
    """Test Genie API with OAuth authentication"""
    if not OAUTH_AVAILABLE or not DATABRICKS_CLIENT_ID or not DATABRICKS_CLIENT_SECRET:
        print("Skipping OAuth authentication test (not available)")
        return
    
    print("\n=== Testing with OAuth Authentication ===")
    
    # Get OAuth headers
    try:
        headers = get_oauth_headers()
        
        # Test start-conversation endpoint
        endpoint = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{SPACE_ID}/start-conversation"
        if ORG_ID:
            endpoint = f"{endpoint}?o={ORG_ID}"
        
        payload = {"content": "Test message from OAuth authentication"}
        
        print(f"POST {endpoint}")
        print(f"Headers: {json.dumps({k: '***' if k == 'Authorization' else v for k, v in headers.items()}, indent=2)}")
        print(f"Payload: {json.dumps(payload, indent=2)}")
        
        response = requests.post(endpoint, headers=headers, json=payload)
        
        print(f"Status Code: {response.status_code}")
        if response.status_code == 200:
            print("OAuth authentication successful!")
            print(f"Response: {json.dumps(response.json(), indent=2)}")
        else:
            print(f"OAuth authentication failed with status code {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error with OAuth authentication: {str(e)}")

def main():
    """Main function to compare authentication methods"""
    print(f"Comparing authentication methods for Databricks Genie API")
    print(f"Host: {DATABRICKS_HOST}")
    print(f"Space ID: {SPACE_ID}")
    print(f"Organization ID: {ORG_ID}")
    
    # Test with PAT
    test_with_pat()
    
    # Test with OAuth
    test_with_oauth()

if __name__ == "__main__":
    main()
