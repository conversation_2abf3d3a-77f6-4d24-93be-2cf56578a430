#!/usr/bin/env python3
"""
Copy Versions to Root Directory

This script copies the web_app_v* folders from the Versions folder to the root directory.
It also copies the oauth_auth.py file and any other necessary files.

Usage:
    python copy_versions.py
"""

import os
import sys
import shutil
import subprocess

def copy_directory(src, dst):
    """Copy a directory from src to dst"""
    try:
        if os.path.exists(dst):
            print(f"{dst} already exists. Skipping.")
            return
        
        print(f"Copying {src} to {dst}...")
        shutil.copytree(src, dst)
        print(f"Successfully copied {src} to {dst}")
    except Exception as e:
        print(f"Error copying {src} to {dst}: {str(e)}")

def copy_file(src, dst):
    """Copy a file from src to dst"""
    try:
        if os.path.exists(dst):
            print(f"{dst} already exists. Skipping.")
            return
        
        print(f"Copying {src} to {dst}...")
        shutil.copy2(src, dst)
        print(f"Successfully copied {src} to {dst}")
    except Exception as e:
        print(f"Error copying {src} to {dst}: {str(e)}")

def copy_versions():
    """Copy the web_app_v* folders from the Versions folder to the root directory"""
    # Check if Versions folder exists
    if not os.path.exists("Versions"):
        print("Versions folder not found. Exiting.")
        return
    
    # Copy web_app_v1
    if os.path.exists("Versions/web_app_v1"):
        copy_directory("Versions/web_app_v1", "web_app_v1")
    else:
        print("web_app_v1 not found in Versions folder.")
    
    # Copy web_app_v2
    if os.path.exists("Versions/web_app_v2"):
        copy_directory("Versions/web_app_v2", "web_app_v2")
    else:
        print("web_app_v2 not found in Versions folder.")
    
    # Copy web_app_v3
    if os.path.exists("Versions/web_app_v3"):
        copy_directory("Versions/web_app_v3", "web_app_v3")
    else:
        print("web_app_v3 not found in Versions folder.")
    
    # Copy oauth_auth.py
    if os.path.exists("Versions/oauth_auth.py"):
        copy_file("Versions/oauth_auth.py", "oauth_auth.py")
    else:
        print("oauth_auth.py not found in Versions folder.")
    
    # Copy assets
    if os.path.exists("Versions/assets") and not os.path.exists("assets"):
        copy_directory("Versions/assets", "assets")
    
    # Copy databricksicon.svg and favicon.svg
    if os.path.exists("Versions/databricksicon.svg"):
        copy_file("Versions/databricksicon.svg", "databricksicon.svg")
    
    if os.path.exists("Versions/favicon.svg"):
        copy_file("Versions/favicon.svg", "favicon.svg")
    
    print("Copying completed.")

if __name__ == "__main__":
    print("Copying Versions to Root Directory")
    print("=================================")
    copy_versions()
    print("Done.")
