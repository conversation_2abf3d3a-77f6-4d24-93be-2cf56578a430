# Files to Delete - Cleanup List

## Documentation Files (Redundant/Outdated)
- `API_HANDLING_FIXES.md`
- `DEPLOYMENT_SUMMARY.md`
- `FILTERING_CONTEXT_FIX.md`
- `FINAL_REPORT.md`
- `FRONTEND_CONSISTENCY_CHANGES.md`
- `GENIE_API_REPORT.md`
- `GENIE_OAUTH_PERMISSIONS.md`
- `GENIE_OAUTH_SOLUTION.md`
- `LOGIN_PAGE_FIXES.md`
- `OAUTH_AUTHENTICATION_FIX.md`
- `OAUTH_README.md`
- `OAUTH_SOLUTION_README.md`
- `SQL_FORMATTING_IMPROVEMENTS.md`
- `TIMEOUT_FIXES.md`
- `TROUBLESHOOTING.md`
- `genie_api_report.md`

## Backup/Archive Files
- `Genie_API_Exploration.zip`
- `genie_deployment.tar.gz`
- `genie_app.bak`
- `genie_app_new.bak`

## Utility Scripts (No Longer Needed)
- `check_images.py`
- `cleanup.py`
- `copy_images.py`
- `copy_images.sh`
- `copy_versions.py`
- `create_favicon.py`
- `setup_logos.py`
- `setup_venv.sh`
- `update_templates.py`
- `sync_assets.py`
- `fix_config.py`
- `fix_config_files.py`
- `fix_genie_apps.py`
- `fix_genie_oauth_permissions.py`
- `fix_genie_permissions.py`

## Test/Debug Scripts
- `test_auth_comparison.py`
- `test_genie_api.py`
- `test_genie_api_urls.py`
- `test_oauth_403.py`
- `test_oauth_url_formats.py`
- `test_pat_auth.py`
- `diagnose_genie_oauth.py`
- `genie_api_explorer.py`
- `genie_cli.py`
- `genie_launcher.py`
- `genie_oauth_fixed.py`
- `genie_oauth_solution.py`
- `simple_genie_demo.py`

## Standalone Apps (Redundant)
- `auth_ui.py`
- `genie_app.py`
- `genie_app_new.py`
- `login_ui.py`
- `main.py`
- `oauth_auth.py` (root level)
- `run_genie_app.py`
- `run_web_app.py`

## Virtual Environments (Should be recreated)
- `genie_env/` (entire directory)
- `genie_venv/` (entire directory)
- `Versions/fresh_venv/` (entire directory)
- `Versions/genie_env/` (entire directory)
- `Versions/venv/` (entire directory)

## Cache/Instance Files
- `__pycache__/` (root level)
- `token_cache.json` (root level)
- `instance/` (root level)

## Duplicate Assets
- `databricks.png` (root level - keep in assets/)
- `databricksicon.svg` (root level)
- `favicon.ico` (root level - keep in assets/)
- `favicon.svg` (root level)
- `tudip.jpeg` (root level - keep in assets/)

## Versions Directory Duplicates
- `Versions/databricks.png`
- `Versions/tudip.jpeg`
- `Versions/databricksicon.svg`
- `Versions/favicon.svg`
- `Versions/assets/` (entire directory - duplicates root assets)

## Redundant Documentation in Versions
- `Versions/Databricks_Genie_API_Integration_Advantages.md`
- `Versions/Databricks_Genie_Web_Integration_Advantages.md`
- `Versions/README.md`

## Web App V1 Excessive Documentation
- `web_app_v1/Alternative_API_Setup.md`
- `web_app_v1/FINAL_SUMMARY.md`
- `web_app_v1/Federation_Alternatives.md`
- `web_app_v1/Federation_Setup_Explained.md`
- `web_app_v1/MANAGER_PRESENTATION.md`
- `web_app_v1/Manager_Presentation_U2M_OAuth.md`
- `web_app_v1/OAUTH_TROUBLESHOOTING.md`
- `web_app_v1/OAuth_Federation_Setup_Guide.md`
- `web_app_v1/Okta_Setup_Guide.md`
- `web_app_v1/Permission_Denied_Solution.md`
- `web_app_v1/README_OAuth_Federation.md`
- `web_app_v1/README_U2M_Implementation.md`
- `web_app_v1/README_U2M_OAuth.md`
- `web_app_v1/TESTING_GUIDE.md`
- `web_app_v1/USER_GUIDE.md`
- `web_app_v1/Web_Interface_Federation_Setup.md`

## Web App V1 Setup/Test Scripts
- `web_app_v1/complete_service_principal_setup.py`
- `web_app_v1/create_federation_policy.sh`
- `web_app_v1/databricks_cli_setup.py`
- `web_app_v1/databricks_federation_setup.py`
- `web_app_v1/demo_u2m_oauth.py`
- `web_app_v1/federation-policy.json`
- `web_app_v1/final_test.py`
- `web_app_v1/find_redirect_uris.py`
- `web_app_v1/fix_database.py`
- `web_app_v1/fix_federation_policy.py`
- `web_app_v1/fix_federation_setup.py`
- `web_app_v1/fix_redirect_uri.py`
- `web_app_v1/generate_federation_curl.py`
- `web_app_v1/migrate_database.py`
- `web_app_v1/reset_database.py`
- `web_app_v1/setup_databricks_federation.py`
- `web_app_v1/setup_demo_federation.py`
- `web_app_v1/setup_federation.py`
- `web_app_v1/simple_app.py`
- `web_app_v1/simple_federation_setup.py`
- `web_app_v1/test_app.py`
- `web_app_v1/test_dynamic_oauth.py`
- `web_app_v1/test_known_uris.py`
- `web_app_v1/test_oauth_url.py`
- `web_app_v1/test_simple_oauth.py`
- `web_app_v1/test_u2m_app.py`
- `web_app_v1/test_u2m_federation.py`
- `web_app_v1/test_u2m_implementation.py`
- `web_app_v1/test_workspace_oauth.py`
- `web_app_v1/verify_account_access.py`

## Redundant Scripts in Versions
- `Versions/oauth_auth.py`
- `Versions/run_web_app.py`
- `Versions/__pycache__/`

## Cache Files in Versions
- `Versions/web_app_v1/__pycache__/`
- `Versions/web_app_v2/__pycache__/`
- `Versions/web_app_v3/__pycache__/`
- `web_app_v1/__pycache__/`
- `web_app_v2/__pycache__/`
- `web_app_v3/__pycache__/`
- `web_app/__pycache__/`

## Token Cache Files
- `web_app_v3/token_cache.json`
- `Versions/web_app_v3/token_cache.json`

## Instance Directories (Database files - backup before deleting)
- `Versions/instance/`
- `web_app_v1/instance/` (contains federation database)
