#!/usr/bin/env python3
"""
Fix Configuration Files for Databricks Genie Web Applications

This script creates config.yaml files for all versions of the Databricks Genie Web Application
if they don't exist, using the config.yaml.example files as templates.

Usage:
    python fix_config_files.py
"""

import os
import sys
import shutil
import yaml

def create_config_file(version_dir):
    """Create config.yaml file for a specific version if it doesn't exist"""
    print(f"\nChecking configuration for {version_dir}...")
    
    config_path = os.path.join(version_dir, "config.yaml")
    example_path = os.path.join(version_dir, "config.yaml.example")
    
    # Check if config file exists
    if not os.path.exists(config_path):
        # Check if example file exists
        if os.path.exists(example_path):
            print(f"Config file not found. Creating from example...")
            shutil.copy(example_path, config_path)
            print(f"Created {config_path}. Please edit it with your settings.")
            return True
        else:
            print(f"Error: config.yaml not found and no example file available.")
            print(f"Creating a default config.yaml file...")
            
            # Create a default config.yaml file
            default_config = {
                "auth": {
                    "method": "oauth",
                    "host": "dbc-620d1468-0f52.cloud.databricks.com",
                    "space_id": "01f02f16a7b11b36a04e4353814a5699?o=****************",
                    "client_id": "9e4f6f53-e2fb-4f7c-a7ce-bf1889db9bdc",
                    "client_secret": "dosebc6041c5004a2289159df91d98a97a66",
                    "account_id": "",
                    "token_cache": {
                        "enabled": True,
                        "cache_file": "token_cache.json",
                        "min_lifetime": 60
                    }
                },
                "ui": {
                    "title": "Databricks Genie",
                    "colors": {
                        "primary": "#FF3621",
                        "secondary": "#1B3139",
                        "accent": "#F9F7F4",
                        "background": "#FFFFFF",
                        "text": "#1B3139"
                    },
                    "logos": {
                        "databricks": "databricks.png",
                        "tudip": "tudip.jpeg",
                        "favicon": "favicon.svg"
                    },
                    "logo_config": {
                        "databricks_height": 60,
                        "tudip_height": 60
                    }
                },
                "flask": {
                    "secret_key": "dev-key-for-databricks-genie",
                    "debug": True,
                    "host": "0.0.0.0",
                    "port": 5000
                }
            }
            
            # Write the default config to the file
            with open(config_path, 'w') as f:
                yaml.dump(default_config, f, default_flow_style=False)
            
            print(f"Created default {config_path}. Please edit it with your settings.")
            return True
    else:
        print(f"Config file already exists at {config_path}.")
        return True

def main():
    """Main function"""
    print("Databricks Genie Web Application Configuration Fixer")
    print("==================================================")
    
    # Create config files for each version
    versions = ["web_app_v1", "web_app_v2", "web_app_v3"]
    
    for version in versions:
        if os.path.exists(version):
            create_config_file(version)
        else:
            print(f"\n{version} directory not found. Skipping.")
    
    print("\nConfiguration check complete.")
    print("\nYou can now run the web applications using:")
    print("  python run_web_app.py")

if __name__ == "__main__":
    main()
