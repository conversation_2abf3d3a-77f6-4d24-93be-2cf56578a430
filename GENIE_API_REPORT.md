# Databricks Genie API: Technical Report

## Executive Summary

This report documents the findings from exploring the Databricks Genie API, which allows programmatic interaction with Genie spaces. The API enables natural language querying of data, SQL generation, and result retrieval through a conversational interface.

Key findings include:

1. The API follows a RESTful design with clear endpoints for conversations and messages
2. The correct URL format is `/api/2.0/genie/spaces/{space_id}` with organization ID as a query parameter
3. Personal Access Token (PAT) authentication is more reliable than OAuth for Genie API access
4. OAuth authentication requires explicit "Can View" permissions on Genie spaces
5. The API supports a conversational model with initial questions and follow-up messages
6. OAuth authentication works for login but has significant limitations for API access
7. The web UI uses a different URL format (`/genie/rooms/{space_id}`) than the API (`/api/2.0/genie/spaces/{space_id}`)
8. Proper handling of the space ID and organization ID is critical for successful API calls

This exploration provides a comprehensive understanding of the API structure, authentication options, and best practices for implementation.

## Introduction

Databricks Genie is an AI-powered assistant that allows business users to query data using natural language. The Genie Conversation API, now in Public Preview, enables developers to integrate this capability into custom applications, chatbots, and other interfaces beyond the Databricks UI.

## API Overview

The Genie API follows RESTful principles and is organized around conversations and messages. A conversation represents a series of interactions with Genie, while messages represent individual questions and responses within that conversation.

### Key Concepts

1. **Spaces**: Pre-configured environments that define the data sources, tables, and context available to Genie
2. **Conversations**: Threads of interaction with a specific Genie space
3. **Messages**: Individual questions or responses within a conversation
4. **Attachments**: Additional content associated with messages, such as SQL queries and results

## API Endpoints

### API URL Format

The Genie API uses the following URL format:

```
https://{databricks-host}/api/2.0/genie/spaces/{space_id}/{endpoint}?o={organization_id}
```

**Important Notes**:
- The correct path for API access is `/api/2.0/genie/spaces/{space_id}` (not `/api/2.0/genie/rooms/` or `/api/2.0/datarooms/`)
- The web UI uses a different URL format: `/genie/rooms/{space_id}?o={organization_id}`
- If your space ID contains a `datarooms/` prefix, it must be removed
- The organization ID parameter (`o={organization_id}`) should be included if present in your Genie space URL
- The organization ID must be added as a query parameter, not as part of the path

### 1. Start Conversation

**Endpoint**: `/api/2.0/genie/spaces/{space_id}/start-conversation`
**Method**: POST
**Purpose**: Initiates a new conversation with an initial question

**Request Format**:
```json
{
    "content": "Your natural language question here"
}
```

**Response Format**:
```json
{
  "conversation_id": "6a64adad2e664ee58de08488f986af3e",
  "conversation": {
    "created_timestamp": 1719769718,
    "conversation_id": "6a64adad2e664ee58de08488f986af3e",
    "last_updated_timestamp": 1719769718,
    "space_id": "3c409c00b54a44c79f79da06b82460e2",
    "title": "Your natural language question here",
    "user_id": 12345
  },
  "message_id": "e1ef34712a29169db030324fd0e1df5f",
  "message": {
    "attachments": null,
    "content": "Your natural language question here",
    "conversation_id": "6a64adad2e664ee58de08488f986af3e",
    "created_timestamp": 1719769718,
    "error": null,
    "message_id": "e1ef34712a29169db030324fd0e1df5f",
    "last_updated_timestamp": 1719769718,
    "query_result": null,
    "space_id": "3c409c00b54a44c79f79da06b82460e2",
    "status": "IN_PROGRESS",
    "user_id": 12345
  }
}
```

### 2. Send Follow-up Message

**Endpoint**: `/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages`
**Method**: POST
**Purpose**: Sends a follow-up question in an existing conversation

**Request Format**:
```json
{
    "content": "Your follow-up question here"
}
```

**Response Format**: Similar to the start conversation response, with a new message_id

### 3. Get Message Status

**Endpoint**: `/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}`
**Method**: GET
**Purpose**: Checks the status of a message and retrieves its details

**Response Format**:
```json
{
  "attachments": [{
    "query": {
      "description": "Query description in a human readable format",
      "last_updated_timestamp": 1719769718,
      "query": "SELECT * FROM customers WHERE date >= CURRENT_DATE() - INTERVAL 1 DAY",
      "title": "Query title",
      "statement_id": "9d8836fc1bdb4729a27fcc07614b52c4",
      "query_result_metadata": {
        "row_count": 10
      },
    },
    "attachment_id": "01efddddeb2510b6a4c125d77ce176be"
  }],
  "content": "Your question here",
  "conversation_id": "6a64adad2e664ee58de08488f986af3e",
  "created_timestamp": 1719769718,
  "error": null,
  "message_id": "e1ef34712a29169db030324fd0e1df5f",
  "last_updated_timestamp": 1719769718,
  "space_id": "3c409c00b54a44c79f79da06b82460e2",
  "status": "EXECUTING_QUERY",
  "user_id": 12345
}
```

### 4. Get Query Results

**Endpoint**: `/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}/attachments/{attachment_id}/query-result`
**Method**: GET
**Purpose**: Retrieves the results of a query generated by Genie

**Response Format**: JSON containing the query results, with structure depending on the query

## Authentication Mechanism

The Genie API supports two authentication methods:

### 1. Personal Access Token (PAT) Authentication

The most reliable method for authenticating with the Genie API is using a Databricks Personal Access Token (PAT). Each request must include an Authorization header with the token:

```
Authorization: Bearer <your-personal-access-token>
```

### 2. OAuth Authentication

The Genie API also supports OAuth authentication, but with important limitations:

```
Authorization: Bearer <your-oauth-token>
```

**OAuth Limitations**:
- OAuth service principals require explicit "Can View" permissions on Genie spaces
- Without proper permissions, OAuth authentication results in 403 Permission Denied errors
- OAuth tokens work for standard Databricks APIs but may fail for Genie-specific endpoints
- OAuth authentication works for login but has significant limitations for API access
- The Databricks Genie API doesn't fully support OAuth authentication for programmatic access
- While the web UI works with OAuth authentication, the API endpoints don't work properly with OAuth

**Authentication Comparison**:
- PAT inherits all permissions of the user who created it
- OAuth service principals have their own separate permission set
- For production applications, PAT is currently more reliable for Genie API access
- OAuth authentication can be used for login but not for full API access

Key observations about the current authentication:

1. **Integration with Databricks Security**: The API leverages existing Databricks security mechanisms
2. **Token Scope**: Standard Databricks tokens are used, which may have broader permissions than needed just for Genie
3. **No Genie-Specific Authentication**: There is no authentication layer specific to Genie beyond the Databricks token
4. **Permission Model**: Genie spaces have their own permission model that affects OAuth access
5. **Web UI vs. API Access**: OAuth authentication works differently for web UI access versus programmatic API access

## Message Processing Flow

The typical flow for interacting with the Genie API is:

1. Start a conversation with an initial question
2. Poll the message status until it changes from "IN_PROGRESS" to either "EXECUTING_QUERY" or "COMPLETED"
3. If the status is "EXECUTING_QUERY", continue polling until "COMPLETED"
4. Once completed, retrieve the query results using the attachment_id
5. For follow-up questions, send a new message to the existing conversation
6. Repeat the polling and result retrieval process

## Best Practices

Based on the API exploration, the following best practices are recommended:

1. **Polling Strategy**:
   - Poll every 5-10 seconds initially
   - Implement exponential backoff for longer-running queries
   - Set a reasonable timeout (e.g., 10 minutes for most queries)

2. **Conversation Management**:
   - Create new conversation threads for each user session
   - Don't reuse conversation IDs across different users
   - Store conversation and message IDs for later reference

3. **Error Handling**:
   - Check for error fields in responses
   - Implement retry logic for transient errors
   - Provide meaningful error messages to users

## Authentication Recommendations

Based on our testing and exploration, we recommend the following approaches for Genie API authentication:

### For Development and Testing

1. **Use Personal Access Tokens (PAT)**:
   - Simplest and most reliable method
   - Inherits all permissions of the user who created it
   - Easy to implement and test

### For Production Applications

1. **Personal Access Token (PAT) Approach** (Recommended):
   - Create a dedicated service account with appropriate permissions
   - Generate a PAT for this service account
   - Rotate tokens periodically according to security policies
   - Store tokens securely in a secrets manager
   - Use PAT for all Genie API interactions

2. **OAuth Approach** (Limited Functionality):
   - Create a service principal in Databricks
   - Ensure the service principal has explicit "Can View" permissions on all Genie spaces it needs to access
   - Request these permissions from your Databricks administrator
   - Implement proper token caching and refresh logic
   - Be aware that OAuth authentication has significant limitations for Genie API access
   - Consider using OAuth only for login and authentication, not for API access
   - For full API access, use PAT authentication

### Additional Security Enhancements

1. **Role-Based Access Control**:
   - Define specific roles for Genie access
   - Control which users/applications can access which spaces
   - Implement fine-grained permissions for different operations

2. **Request Validation**:
   - Implement request signing for additional security
   - Add request timestamps and nonces to prevent replay attacks
   - Validate request integrity

3. **Rate Limiting and Quotas**:
   - Implement per-user or per-token rate limits
   - Set quotas for conversation and message creation
   - Monitor and alert on unusual usage patterns

## Integration Examples

The Genie API can be integrated into various applications:

1. **Chat Applications**: Microsoft Teams, Slack, Discord
2. **Custom Web Applications**: Internal dashboards, customer portals
3. **Mobile Applications**: iOS and Android apps for field workers
4. **Automated Reporting**: Scheduled data extraction and reporting
5. **Multi-Agent Systems**: As part of a larger AI agent framework

## Conclusion

The Databricks Genie API provides a powerful way to interact with data using natural language. Our exploration has revealed several key findings:

1. **API Structure**: The API follows RESTful principles with a clear structure for spaces, conversations, and messages.

2. **URL Format**:
   - The correct URL format for API access is `/api/2.0/genie/spaces/{space_id}` with any `datarooms/` prefix removed from the space ID
   - The web UI uses a different URL format: `/genie/rooms/{space_id}?o={organization_id}`
   - The organization ID must be added as a query parameter, not as part of the path

3. **Authentication Options**:
   - Personal Access Token (PAT) authentication is the most reliable method
   - OAuth authentication works for standard Databricks APIs but requires explicit permissions for Genie spaces
   - OAuth authentication works for login but has significant limitations for API access
   - The Databricks Genie API doesn't fully support OAuth authentication for programmatic access
   - Without proper permissions, OAuth authentication results in 403 Permission Denied errors

4. **Space ID and Organization ID Handling**:
   - Proper extraction and handling of the space ID and organization ID is critical for successful API calls
   - The organization ID should be extracted from the full space ID string and added as a query parameter
   - Any `datarooms/` prefix should be removed from the space ID

5. **Best Practices**:
   - Use PAT authentication for development and testing
   - For production, use a service account with PAT for full API access
   - If OAuth is required, be aware of its limitations for Genie API access
   - Implement appropriate polling strategies and error handling

The API's conversational nature makes it well-suited for integration into chat applications and other interfaces where users need to explore data through natural language queries. By following the best practices outlined in this report, developers can create robust integrations that provide a good user experience while maintaining security and performance.

## Appendix: Sample Code and Logs

1. **Sample Code**:
   - `genie_app.py`: Main application for interacting with the Genie API
   - `login_ui.py`: Login interface supporting both PAT and OAuth authentication
   - `oauth_auth.py`: OAuth authentication implementation

2. **Authentication Tests**:
   - `test_auth_comparison.py`: Demonstrates the differences between PAT and OAuth authentication
   - `genie_cli.py`: Command-line interface for testing Genie API access

3. **Key Findings**:
   - PAT authentication works fully with the Genie API
   - OAuth authentication works for login but has significant limitations for API access
   - The web UI uses a different URL format than the API
   - Proper handling of the space ID and organization ID is critical for successful API calls

4. **Log Files**:
   - `genie_api_exploration_2025-05-12_19-03-22.log`: Successful API calls using PAT authentication
   - `genie_api_exploration_2025-05-12_19-02-31.log`: Failed API calls using OAuth authentication (403 Permission Denied)
   - `genie_api_exploration_2025-05-12_16-08-20.log`: Original exploration log with detailed API responses
