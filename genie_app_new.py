#!/usr/bin/env python3
"""
Databricks Genie Application

This script provides the main application interface for interacting with the Databricks Genie API.
It is launched after successful authentication through the login UI.

Usage:
    python genie_app.py
"""

import os
import sys
import time
import webbrowser

# Try to import required packages
try:
    import requests
    from dotenv import load_dotenv
    REQUIRED_PACKAGES_AVAILABLE = True
except ImportError:
    REQUIRED_PACKAGES_AVAILABLE = False
    print("Error: Required packages are not installed.")
    print("To install required packages:")
    print("  pip install python-dotenv requests")
    print("\nAlternatively, you can install all requirements:")
    print("  pip install -r requirements.txt")
    sys.exit(1)

# Try to import tkinter
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext
    TKINTER_AVAILABLE = True
except ImportError:
    TKINTER_AVAILABLE = False
    print("Error: tkinter is not installed. This UI requires tkinter.")
    print("To install tkinter:")
    print("  - On Ubuntu/Debian: sudo apt-get install python3-tk")
    print("  - On Fedora/RHEL: sudo dnf install python3-tkinter")
    print("  - On macOS: brew install python-tk")
    print("  - On Windows: tkinter is included with Python by default")
    sys.exit(1)

# Try to import OAuth authentication
try:
    from oauth_auth import get_auth_headers
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables
load_dotenv()

class GenieApp:
    def __init__(self, root, auth_type="pat"):
        self.root = root
        self.root.title(f"Databricks Genie - {auth_type.upper()} Authentication")
        self.root.geometry("900x700")
        self.root.minsize(700, 500)

        # Set background color
        self.root.configure(bg="#f8f9fa")

        # Set application icon if available
        try:
            self.root.iconbitmap("assets/databricksicon.png")
        except:
            pass

        # Configure styles
        self.configure_styles()

        # Store authentication type
        self.auth_type = auth_type

        # Flag to track if authentication is successful
        self.auth_successful = False

        # Load configuration
        self.load_config()

        # Create main layout
        self.create_layout()

        # Initialize conversation history
        self.conversation_id = None
        self.message_history = []

        # Set initial status
        self.status_var.set(f"Authenticating with {auth_type.upper()}...")

        # Test authentication before allowing conversation
        self.test_authentication()

    def show_auth_status_dialog(self, title, message, checklist_items, footer_text):
        """Show a custom dialog for authentication status with a checklist"""
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("550x450")
        dialog.minsize(450, 350)
        dialog.transient(self.root)
        dialog.grab_set()
        
        # Set background color
        dialog.configure(bg="#f8f9fa")
        
        # Create main frame with padding
        main_frame = ttk.Frame(dialog, padding="20", style="Main.TFrame")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create icon and message
        icon_frame = ttk.Frame(main_frame, style="Main.TFrame")
        icon_frame.pack(fill=tk.X, pady=(0, 15))
        
        # Use a Unicode character as an icon
        if "Error" in title or "Denied" in title:
            icon_text = "⚠️"  # Warning icon
            icon_color = "#dc3545"  # Red for errors
        else:
            icon_text = "ℹ️"  # Information icon
            icon_color = "#0062cc"  # Blue for info
            
        icon_label = ttk.Label(
            icon_frame,
            text=icon_text,
            font=("Helvetica", 28),
            background="#f8f9fa",
            foreground=icon_color
        )
        icon_label.pack(side=tk.LEFT, padx=(0, 15))
        
        # Message
        message_label = ttk.Label(
            icon_frame,
            text=message,
            font=("Helvetica", 12),
            background="#f8f9fa",
            foreground="#212529",
            wraplength=400
        )
        message_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Create checklist frame
        checklist_frame = ttk.LabelFrame(
            main_frame,
            text="Please verify:",
            padding="15",
            style="Conversation.TLabelframe"
        )
        checklist_frame.pack(fill=tk.X, pady=15)
        
        # Add checklist items
        for i, item in enumerate(checklist_items):
            item_frame = ttk.Frame(checklist_frame, style="Conversation.TLabelframe")
            item_frame.pack(fill=tk.X, pady=5)
            
            bullet_label = ttk.Label(
                item_frame,
                text="•",
                font=("Helvetica", 14, "bold"),
                background="#ffffff",
                foreground="#0062cc"
            )
            bullet_label.pack(side=tk.LEFT, padx=(0, 8))
            
            item_label = ttk.Label(
                item_frame,
                text=item,
                font=("Helvetica", 11),
                background="#ffffff",
                foreground="#212529",
                wraplength=400
            )
            item_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        # Footer text with API error details
        if footer_text:
            footer_frame = ttk.Frame(main_frame, style="Main.TFrame")
            footer_frame.pack(fill=tk.X, pady=(15, 0))
            
            # Split the footer text to extract API error details if present
            api_error_details = None
            main_footer_text = footer_text
            
            if "Error details:" in footer_text:
                parts = footer_text.split("Error details:", 1)
                main_footer_text = parts[0].strip()
                if len(parts) > 1:
                    api_error_details = parts[1].strip()
            
            # Main footer text
            footer_label = ttk.Label(
                footer_frame,
                text=main_footer_text,
                font=("Helvetica", 10),
                background="#f8f9fa",
                foreground="#495057",
                wraplength=500,
                justify=tk.LEFT
            )
            footer_label.pack(fill=tk.X)
            
            # API error details in a scrolled text if present
            if api_error_details:
                error_frame = ttk.LabelFrame(
                    main_frame,
                    text="API Error Details",
                    padding="10",
                    style="Conversation.TLabelframe"
                )
                error_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))
                
                error_text = scrolledtext.ScrolledText(
                    error_frame,
                    wrap=tk.WORD,
                    width=50,
                    height=5,
                    font=("Courier New", 9),
                    background="#f8f9fa",
                    foreground="#dc3545"
                )
                error_text.pack(fill=tk.BOTH, expand=True)
                error_text.insert(tk.END, api_error_details)
                error_text.config(state=tk.DISABLED)
        
        # OK button
        button_frame = ttk.Frame(main_frame, style="Main.TFrame")
        button_frame.pack(fill=tk.X, pady=(15, 0))
        
        ok_button = ttk.Button(
            button_frame,
            text="OK",
            command=dialog.destroy,
            style="Action.TButton",
            width=10
        )
        ok_button.pack(side=tk.RIGHT)
        
        # Center the dialog on the parent window
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() - dialog.winfo_width()) // 2
        y = self.root.winfo_y() + (self.root.winfo_height() - dialog.winfo_height()) // 2
        dialog.geometry(f"+{x}+{y}")
        
        # Make dialog modal
        dialog.focus_set()
        dialog.wait_window()
