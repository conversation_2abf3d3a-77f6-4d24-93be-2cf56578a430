# Fixing Databricks Genie API OAuth Authentication Permissions

This document explains how to fix the 403 "PERMISSION_DENIED" error when accessing the Databricks Genie API with OAuth authentication.

## The Problem

When using OAuth authentication with the Databricks Genie API, you may encounter the following error:

```json
{
  "error_code": "PERMISSION_DENIED",
  "message": "You need \"Can View\" permission to perform this action",
  "details": [
    {
      "@type": "type.googleapis.com/google.rpc.RequestInfo",
      "request_id": "d0210cad-9c9d-4303-91bd-3cce17b3706b",
      "serving_data": ""
    }
  ]
}
```

This error occurs because the service principal used for OAuth authentication doesn't have the necessary permissions to access the Genie space.

## The Solution

To fix this issue, you need to grant the service principal "Can View" permission on the Genie spaces you want to access.

### Step 1: Identify Your Service Principal

Your service principal is identified by the `DATABRICKS_CLIENT_ID` in your `.env` file. In your case, the service principal is:

- **Name**: Service_principal_1
- **ID**: 3231051697716437
- **Application ID**: 9e4f6f53-e2fb-4f7c-a7ce-bf1889db9bdc

### Step 2: Grant Permissions to Your Service Principal

1. Log in to your Databricks workspace: https://dbc-620d1468-0f52.cloud.databricks.com
2. Navigate to the Genie section in the sidebar
3. For each Genie space you want to access:
   a. Open the space
   b. Click the "Share" button in the top-right corner
   c. Search for the service principal named "Service_principal_1" (ID: 3231051697716437)
   d. Grant at least "Can View" permission to the service principal
   e. Click "Save" to apply the permissions

### Step 3: Additional Steps for Workspace Administrators

If you're a workspace administrator, you may need to take additional steps:

1. Go to the Databricks workspace settings
2. Navigate to the "Admin Console" section
3. Go to the "Users and Groups" tab
4. Find your service principal
5. Ensure it has the necessary permissions to access Genie spaces
6. You may need to add the service principal to a group with appropriate permissions

## Understanding Genie Space Permissions

According to the [Databricks documentation](https://docs.databricks.com/aws/en/security/auth/access-control/#genie-space-acls), Genie space permissions work as follows:

| Ability | NO PERMISSIONS | CAN VIEW/CAN RUN | CAN EDIT | CAN MANAGE |
|---------|---------------|-----------------|----------|------------|
| See in Genie space list | | x | x | x |
| Ask Genie questions | | x | x | x |
| Provide response feedback | | x | x | x |
| Add or edit Genie instructions | | | x | x |
| Add or edit sample questions | | | x | x |
| Add or remove included tables | | | x | x |
| Monitor a space | | | | x |
| Modify permissions | | | | x |
| Delete space | | | | x |
| View other users' conversations | | | x | x |

For OAuth authentication to work, your service principal needs at least "CAN VIEW/CAN RUN" permission on the Genie space.

## Testing the Fix

After granting the necessary permissions, you can test if the fix worked by running:

```bash
python fix_genie_oauth_permissions.py
```

If the permissions have been correctly applied, you should see:

```
=== Testing OAuth Access to Genie API ===
Testing URL: https://dbc-620d1468-0f52.cloud.databricks.com/api/2.0/genie/spaces/datarooms?o=1883526265026134
Status Code: 200
✅ OAuth authentication to Genie API successful!
Found X Genie spaces
```

## Additional Resources

- [Databricks Genie Space ACLs Documentation](https://docs.databricks.com/aws/en/security/auth/access-control/#genie-space-acls)
- [Set up and manage an AI/BI Genie space](https://docs.databricks.com/aws/en/genie/set-up)
- [Databricks OAuth Authentication](https://docs.databricks.com/aws/en/dev-tools/auth/oauth-m2m)

## Troubleshooting

If you continue to experience issues after following these steps:

1. Verify that your service principal has been correctly added to the Genie space with the appropriate permissions
2. Check if your service principal has the necessary permissions at the workspace level
3. Ensure that your OAuth credentials (client ID and client secret) are correct
4. Try using a Personal Access Token (PAT) authentication as a temporary workaround
5. Contact your Databricks workspace administrator for assistance

## Understanding the Error

The error "You need 'Can View' permission to perform this action" is a standard Databricks permission error. It indicates that the authenticated identity (in this case, your service principal) doesn't have the minimum required permission level to access the requested resource.

In Databricks, permissions are hierarchical:
- CAN MANAGE > CAN EDIT > CAN RUN/CAN VIEW > NO PERMISSIONS

For Genie spaces, "CAN VIEW" and "CAN RUN" are equivalent and represent the minimum permission level needed to interact with a Genie space.
