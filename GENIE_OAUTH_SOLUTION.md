# Databricks Genie API OAuth Authentication Solution

This document explains how to use OAuth authentication with the Databricks Genie API.

## The Problem

When using OAuth authentication with the Databricks Genie API, you may encounter a 403 "PERMISSION_DENIED" error with the message "You need 'Can View' permission to perform this action". This happens even after granting the service principal the necessary permissions on the Genie space.

The root cause of this issue was using incorrect API URL formats. The Databricks Genie API requires specific URL formats for different operations.

## The Solution

We've created a solution that uses the correct API URL formats for OAuth authentication with the Databricks Genie API. The key components of the solution are:

1. **Correct API URL Formats**:
   - For accessing a specific Genie space: 
     ```
     https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}?o={org_id}
     ```
   - For starting a conversation:
     ```
     https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/start-conversation?o={org_id}
     ```
   - For getting a message:
     ```
     https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}/conversations/{conversation_id}/messages/{message_id}?o={org_id}
     ```

2. **OAuth Authentication**:
   - The solution uses the OAuth client credentials flow to obtain an access token
   - The token is cached and refreshed automatically when it expires

3. **Message Processing**:
   - The solution handles various message statuses, including "ASKING_AI" and "PENDING_WAREHOUSE"
   - It waits for the message to complete before returning the response

## How to Use the Solution

### Prerequisites

1. Make sure you have the following environment variables set in your `.env` file:
   - `DATABRICKS_HOST`: Your Databricks workspace hostname (e.g., `dbc-620d1468-0f52.cloud.databricks.com`)
   - `DATABRICKS_CLIENT_ID`: Your OAuth client ID
   - `DATABRICKS_CLIENT_SECRET`: Your OAuth client secret
   - `DATABRICKS_SPACE_ID`: Your Genie space ID (e.g., `01f02f16a7b11b36a04e4353814a5699?o=1883526265026134`)

2. Make sure your service principal has at least "Can View" permission on the Genie space

### Using the Solution

The solution provides several functions for interacting with the Databricks Genie API:

1. **get_oauth_token()**: Get an OAuth token for authenticating with the Databricks API
2. **get_genie_space()**: Get information about a specific Genie space
3. **start_conversation()**: Start a new conversation in a Genie space
4. **get_message()**: Get a message from a conversation
5. **wait_for_message_completion()**: Wait for a message to complete processing
6. **extract_response_content()**: Extract the response content from a message
7. **ask_genie()**: Ask a question to Genie and get the response

Here's a simple example of how to use the solution:

```python
from genie_oauth_solution import ask_genie

# Ask a question to Genie
question = "Show me the top 10 customers by revenue"
response = ask_genie(question)

# Print the response
print(response)
```

## Troubleshooting

If you encounter issues with the solution, try the following:

1. **Check your OAuth credentials**:
   - Make sure your client ID and client secret are correct
   - Verify that your service principal has the necessary permissions

2. **Check your Genie space ID**:
   - Make sure the space ID is correct
   - Include the organization ID parameter (`o=...`) if it's in your space URL

3. **Check the API response**:
   - If you get a 404 error, the API endpoint might be incorrect
   - If you get a 403 error, you might not have the necessary permissions

4. **Increase the wait time**:
   - If you get a timeout error, try increasing the `max_wait_seconds` parameter

## Files Included

1. **genie_oauth_solution.py**: The main solution file with all the functions for OAuth authentication with the Genie API
2. **diagnose_genie_oauth.py**: A diagnostic script that helps identify issues with OAuth authentication
3. **test_genie_api_urls.py**: A script that tests different API URL formats to find the correct ones
4. **genie_oauth_fixed.py**: A more verbose version of the solution with detailed logging

## Conclusion

The solution provides a reliable way to use OAuth authentication with the Databricks Genie API. By using the correct API URL formats and handling the various message statuses, you can now successfully interact with the Genie API using OAuth authentication.

If you have any questions or need further assistance, please don't hesitate to reach out.
