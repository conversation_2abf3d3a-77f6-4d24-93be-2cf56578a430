#!/usr/bin/env python3
"""
Databricks Genie Application

This script provides the main application interface for interacting with the Databricks Genie API.
It is launched after successful authentication through the login UI.

Usage:
    python genie_app.py
"""

import os
import sys
import time
import json
import webbrowser

# Try to import required packages
try:
    import requests
    from dotenv import load_dotenv
    REQUIRED_PACKAGES_AVAILABLE = True
except ImportError:
    REQUIRED_PACKAGES_AVAILABLE = False
    print("Error: Required packages are not installed.")
    print("To install required packages:")
    print("  pip install python-dotenv requests")
    print("\nAlternatively, you can install all requirements:")
    print("  pip install -r requirements.txt")
    sys.exit(1)

# Try to import tkinter
try:
    import tkinter as tk
    from tkinter import ttk, messagebox, scrolledtext, font
    TKINTER_AVAILABLE = True
except ImportError:
    TKINTER_AVAILABLE = False
    print("Error: tkinter is not installed. This UI requires tkinter.")
    print("To install tkinter:")
    print("  - On Ubuntu/Debian: sudo apt-get install python3-tk")
    print("  - On Fedora/RHEL: sudo dnf install python3-tkinter")
    print("  - On macOS: brew install python-tk")
    print("  - On Windows: tkinter is included with Python by default")
    sys.exit(1)

# Try to import PIL for image processing
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL/Pillow is not installed. Some UI features may be limited.")
    print("To install PIL/Pillow:")
    print("  pip install pillow")

# Try to import OAuth authentication
try:
    from oauth_auth import get_auth_headers
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables
load_dotenv()

class GenieApp:
    def __init__(self, root, auth_type="pat"):
        self.root = root
        self.root.title(f"Databricks Genie - {auth_type.upper()} Authentication")
        self.root.geometry("1000x800")
        self.root.minsize(800, 600)

        # Set background color
        self.root.configure(bg="#FFFFFF")

        # Set application icon if available
        try:
            self.root.iconbitmap("assets/favicon.ico")
        except:
            try:
                # Try using a PNG as icon
                icon = tk.PhotoImage(file="assets/databricks.png")
                self.root.iconphoto(True, icon)
            except:
                pass

        # Configure styles
        self.configure_styles()

        # Store authentication type
        self.auth_type = auth_type

        # Store the current auth type in environment variable for persistence
        os.environ["CURRENT_AUTH_TYPE"] = auth_type

        # Flag to track if authentication is successful
        self.auth_successful = False

        # Load configuration
        self.load_config()

        # Create main layout
        self.create_layout()

        # Initialize conversation history
        self.conversation_id = None
        self.message_history = []

        # Set initial status
        self.status_var.set(f"Authenticating with {auth_type.upper()}...")

        # Test authentication before allowing conversation
        self.test_authentication()

    def test_authentication(self):
        """Test authentication before allowing conversation"""
        try:
            # For OAuth, we should be able to access the Genie space directly
            if self.auth_type == "oauth":
                try:
                    # Import the OAuth URL functions
                    from oauth_auth import get_genie_space_url

                    # Get the correct URL for accessing the Genie space
                    endpoint = get_genie_space_url(self.space_id)

                    # Send the request with timeout
                    response = requests.get(endpoint, headers=self.headers, timeout=10)
                except ImportError:
                    # Fall back to the old method if the OAuth module is not available
                    endpoint = f"https://{self.host}/api/2.0/genie/spaces/{self.space_id}"

                    # Add organization ID as a query parameter if available
                    if hasattr(self, 'org_id') and self.org_id:
                        endpoint += f"?o={self.org_id}"

                    # Send the request with timeout
                    response = requests.get(endpoint, headers=self.headers, timeout=10)

                if response.status_code == 200:
                    # This is unexpected for OAuth but we'll accept it
                    self.auth_successful = True
                    self.status_var.set("OAuth authentication successful (unexpected)")
                    self.show_welcome_message()
                elif response.status_code == 403:
                    # This is expected for OAuth
                    self.auth_successful = True
                    self.status_var.set("OAuth authentication verified (403 Permission Denied)")
                    self.show_welcome_message()

                    # Show a message explaining the 403 error
                    oauth_msg = (
                        "OAuth Authentication Status: Permission Denied (403)\n\n"
                        "Error: Your OAuth credentials don't have access to this Genie space.\n\n"
                        "This is the expected behavior for OAuth authentication with the Genie API.\n"
                        "Please verify:\n"
                        "• Your service principal has been granted access to this specific Genie space\n"
                        "• The Space ID is correct\n"
                        "• Your OAuth credentials have the necessary permissions\n\n"
                        "You can:\n"
                        "• Continue using OAuth (some features may be limited)\n"
                        "• Switch to PAT authentication using the 'Switch to PAT' button\n"
                    )
                    self.append_to_conversation(oauth_msg, "System")

                    # Show a custom dialog with better formatting
                    self.show_auth_status_dialog(
                        "Permission Denied",
                        "Your OAuth credentials don't have access to this Genie space.",
                        [
                            "Your service principal has been granted access to this specific Genie space",
                            "The Space ID is correct",
                            "Your OAuth credentials have the necessary permissions"
                        ],
                        "This is the expected error for OAuth authentication. You can proceed to use the application."
                    )
                else:
                    # Unexpected error
                    error_details = response.text[:200] if response.text else 'No details available'
                    error_msg = (
                        f"OAuth Authentication Error: {response.status_code}\n\n"
                        f"Error: The API returned an unexpected error code {response.status_code}.\n\n"
                        f"This may indicate an issue with your OAuth credentials or the Genie API.\n\n"
                        f"Error details: {error_details}\n\n"
                        f"You can try switching to PAT authentication using the 'Switch to PAT' button."
                    )
                    self.append_to_conversation(error_msg, "System")

                    # Show a custom dialog with better formatting
                    self.show_auth_status_dialog(
                        f"Unexpected OAuth Error ({response.status_code})",
                        f"The API returned an unexpected error code {response.status_code}. This may indicate an issue with your OAuth credentials or the Genie API.",
                        [
                            "Your OAuth client ID and secret are correct",
                            "Your OAuth credentials have not expired",
                            "The Databricks host is correct"
                        ],
                        f"Error details: {error_details}\n\nYou can try switching to PAT authentication using the 'Switch to PAT' button."
                    )
            else:  # PAT authentication
                # Use the spaces endpoint to test PAT authentication
                endpoint = f"https://{self.host}/api/2.0/genie/spaces/{self.space_id}"

                # Add organization ID as a query parameter if available
                if hasattr(self, 'org_id') and self.org_id:
                    endpoint += f"?o={self.org_id}"

                # Send the request with timeout
                response = requests.get(endpoint, headers=self.headers, timeout=10)

                if response.status_code == 200:
                    # PAT authentication successful
                    self.auth_successful = True
                    self.status_var.set("PAT authentication successful")
                    self.show_welcome_message()
                else:
                    # PAT authentication failed
                    error_details = response.text[:200] if response.text else 'No details available'
                    error_msg = (
                        f"PAT Authentication Error: {response.status_code}\n\n"
                        f"Error: The API returned an error code {response.status_code}.\n\n"
                        f"This may indicate an issue with your PAT token or the Genie API.\n\n"
                        f"Error details: {error_details}\n\n"
                        f"You can try switching to OAuth authentication using the 'Switch to OAuth' button."
                    )
                    self.append_to_conversation(error_msg, "System")

                    # Show a custom dialog with better formatting
                    self.show_auth_status_dialog(
                        f"Authentication Error ({response.status_code})",
                        f"The API returned an error code {response.status_code}. This may indicate an issue with your PAT token or the Genie API.",
                        [
                            "Your PAT token is valid and has not expired",
                            "The Space ID is correct",
                            "Your PAT token has the necessary permissions"
                        ],
                        f"Error details: {error_details}\n\nYou can try switching to OAuth authentication using the 'Switch to OAuth' button."
                    )
        except Exception as e:
            # Authentication test failed
            error_msg = (
                f"Authentication Test Error\n\n"
                f"Error: An error occurred while testing authentication.\n\n"
                f"Error details: {str(e)}\n\n"
                f"You can try switching authentication methods using the buttons below."
            )
            self.append_to_conversation(error_msg, "System")

            # Show a custom dialog with better formatting
            self.show_auth_status_dialog(
                "Authentication Error",
                "An error occurred while testing authentication.",
                [
                    "Your connection settings are correct",
                    "Your credentials are valid",
                    "The Databricks host is reachable"
                ],
                f"Error details: {str(e)}\n\nYou can try switching authentication methods using the buttons below."
            )

    def show_welcome_message(self):
        """Show welcome message after successful authentication"""
        welcome_msg = (
            "Welcome to Databricks Genie!\n\n"
            "You can ask questions about your data in natural language, and Genie will generate SQL "
            "queries and visualizations to help you explore your data.\n\n"
            "Try asking something like:\n"
            "• Show me the top 10 customers by revenue\n"
            "• What were our sales last quarter?\n"
            "• How has our user growth changed over time?\n"
        )
        self.append_to_conversation(welcome_msg, "Genie")

    def configure_styles(self):
        """Configure custom styles for the UI with Databricks theme"""
        style = ttk.Style()

        # Set theme if available
        try:
            style.theme_use("clam")  # Use a more modern theme if available
        except:
            pass

        # Define Databricks colors
        bg_color = "#FFFFFF"
        databricks_red = "#FF3621"
        databricks_dark_red = "#DB1B0B"
        databricks_blue = "#2196F3"
        databricks_dark_blue = "#0D47A1"
        databricks_light_blue = "#E3F2FD"
        databricks_gray = "#F5F5F5"
        databricks_dark_gray = "#333333"
        databricks_light_gray = "#EEEEEE"
        databricks_text = "#212121"
        databricks_secondary_text = "#757575"
        databricks_border = "#DDDDDD"
        tudip_blue = "#0066CC"

        # Configure frame styles
        style.configure("Main.TFrame", background=bg_color)
        style.configure("Header.TFrame", background=bg_color)
        style.configure("Footer.TFrame", background=databricks_light_gray)
        style.configure("Conversation.TLabelframe",
                       background=bg_color,
                       borderwidth=1,
                       relief="solid")
        style.configure("Conversation.TLabelframe.Label",
                       font=("Segoe UI", 12, "bold"),
                       background=bg_color,
                       foreground=databricks_text)
        style.configure("Controls.TFrame", background=bg_color)
        style.configure("StatusBar.TLabel",
                       background=databricks_light_gray,
                       padding=5,
                       foreground=databricks_secondary_text)
        style.configure("Auth.TLabel",
                       background=bg_color,
                       foreground=databricks_red,
                       font=("Segoe UI", 10, "bold"))

        # Configure button styles
        style.configure("Action.TButton",
                       font=("Segoe UI", 10),
                       background=databricks_red,
                       foreground="white",
                       padding=(10, 5))
        style.map("Action.TButton",
                 background=[("active", databricks_dark_red), ("pressed", databricks_dark_red)],
                 foreground=[("active", "white"), ("pressed", "white")])

        style.configure("Send.TButton",
                       font=("Segoe UI", 10, "bold"),
                       background=databricks_red,
                       foreground="white",
                       padding=(15, 8))
        style.map("Send.TButton",
                 background=[("active", databricks_dark_red), ("pressed", databricks_dark_red)],
                 foreground=[("active", "white"), ("pressed", "white")])

        style.configure("Secondary.TButton",
                       font=("Segoe UI", 10),
                       background=bg_color,
                       foreground=databricks_red,
                       padding=(10, 5))
        style.map("Secondary.TButton",
                 background=[("active", databricks_light_gray), ("pressed", databricks_light_gray)],
                 foreground=[("active", databricks_dark_red), ("pressed", databricks_dark_red)])

        # Configure entry styles
        style.configure("TEntry",
                       padding=8,
                       relief="solid",
                       background="#ffffff",
                       fieldbackground="#ffffff",
                       borderwidth=1)
        style.map("TEntry",
                 bordercolor=[("focus", databricks_red)])

        # Configure message styles for the conversation
        self.user_message_style = {
            "bg": databricks_light_blue,
            "fg": databricks_dark_blue,
            "font": ("Segoe UI", 11),
            "border_color": databricks_blue,
            "border_width": 1,
            "padx": 15,
            "pady": 10,
            "radius": 10
        }

        self.genie_message_style = {
            "bg": bg_color,
            "fg": databricks_text,
            "font": ("Segoe UI", 11),
            "border_color": databricks_light_gray,
            "border_width": 1,
            "padx": 15,
            "pady": 10,
            "radius": 10
        }

        self.system_message_style = {
            "bg": databricks_light_gray,
            "fg": databricks_secondary_text,
            "font": ("Segoe UI", 10, "italic"),
            "border_color": databricks_border,
            "border_width": 1,
            "padx": 15,
            "pady": 10,
            "radius": 10
        }

    def load_config(self):
        """Load configuration from environment variables"""
        # Reload environment variables to ensure we have the latest values
        from dotenv import load_dotenv, find_dotenv
        dotenv_path = find_dotenv()
        if dotenv_path:
            load_dotenv(dotenv_path, override=True)

        # Get host and space ID
        self.host = os.getenv("DATABRICKS_HOST", "").strip()
        space_id_full = os.getenv("DATABRICKS_SPACE_ID", "").strip()

        # Extract space ID and org ID if present
        if "?" in space_id_full:
            self.space_id = space_id_full.split("?")[0]
            org_id_part = space_id_full.split("?")[1]
            if "o=" in org_id_part:
                self.org_id = org_id_part.split("o=")[1]
            else:
                self.org_id = None
        else:
            self.space_id = space_id_full
            self.org_id = None

        # Remove any 'datarooms/' prefix if it exists in the space_id
        if self.space_id.startswith('datarooms/'):
            self.space_id = self.space_id.replace('datarooms/', '')

        # Print debug information about the space ID
        print(f"Using Space ID: {self.space_id}")
        if self.org_id:
            print(f"Using Organization ID: {self.org_id}")

        # Check if we should use the auth type from environment variable
        env_auth_type = os.getenv("CURRENT_AUTH_TYPE", "").lower()
        if env_auth_type in ["pat", "oauth"] and env_auth_type != self.auth_type:
            print(f"Switching auth type from {self.auth_type} to {env_auth_type} based on environment variable")
            self.auth_type = env_auth_type

        # Set up authentication headers
        if self.auth_type == "oauth" and OAUTH_AVAILABLE:
            try:
                # Import OAuth module and get headers
                from oauth_auth import get_auth_headers, get_genie_space_url
                self.headers = get_auth_headers()

                # Print debug information
                print(f"Using OAuth authentication with client ID: {os.getenv('DATABRICKS_CLIENT_ID', '')[:5]}...")

                # Test OAuth authentication with the Genie space API
                try:
                    # Get the correct URL for accessing the Genie space
                    test_url = get_genie_space_url(self.space_id)
                    print(f"Testing OAuth authentication with Genie space API: {test_url}")

                    test_response = requests.get(test_url, headers=self.headers, timeout=10)
                    if test_response.status_code == 200:
                        print("OAuth authentication with Genie space API successful!")
                        print(f"Response: {json.dumps(test_response.json(), indent=2)[:200]}...")
                    else:
                        print(f"OAuth authentication with Genie space API failed with status code {test_response.status_code}")
                        print(f"Response: {test_response.text}")

                        # Use our custom dialog instead of messagebox
                        self.show_auth_status_dialog(
                            "Authentication Warning",
                            "OAuth authentication with Genie API failed. Falling back to PAT authentication.",
                            ["Check your OAuth credentials and try again"],
                            f"Error details: Status code {test_response.status_code}, Response: {test_response.text}"
                        )

                        # Fall back to PAT
                        token = os.getenv("DATABRICKS_TOKEN", "").strip()
                        if token:
                            self.headers = {
                                "Authorization": f"Bearer {token}",
                                "Content-Type": "application/json"
                            }
                            self.auth_type = "pat"
                        else:
                            self.show_auth_status_dialog(
                                "Authentication Error",
                                "OAuth authentication failed and no PAT token available for fallback.",
                                ["Add a PAT token to your .env file"],
                                ""
                            )
                except ImportError:
                    # Fall back to the old method if the OAuth URL functions are not available
                    test_url = f"https://{self.host}/api/2.0/clusters/list"
                    test_response = requests.get(test_url, headers=self.headers, timeout=5)
                    if test_response.status_code != 200:
                        # Use our custom dialog instead of messagebox
                        self.show_auth_status_dialog(
                            "Authentication Warning",
                            "OAuth authentication failed. Falling back to PAT authentication.",
                            ["Check your OAuth credentials and try again"],
                            f"Error details: Status code {test_response.status_code}"
                        )

                        # Fall back to PAT
                        token = os.getenv("DATABRICKS_TOKEN", "").strip()
                        if token:
                            self.headers = {
                                "Authorization": f"Bearer {token}",
                                "Content-Type": "application/json"
                            }
                            self.auth_type = "pat"
                        else:
                            self.show_auth_status_dialog(
                                "Authentication Error",
                                "OAuth authentication failed and no PAT token available for fallback.",
                                ["Add a PAT token to your .env file"],
                                ""
                            )
                except Exception as e:
                    # Use our custom dialog instead of messagebox
                    self.show_auth_status_dialog(
                        "Authentication Warning",
                        f"OAuth authentication test failed. Falling back to PAT authentication.",
                        ["Check your OAuth credentials and try again"],
                        f"Error details: {str(e)}"
                    )

                    # Fall back to PAT
                    token = os.getenv("DATABRICKS_TOKEN", "").strip()
                    if token:
                        self.headers = {
                            "Authorization": f"Bearer {token}",
                            "Content-Type": "application/json"
                        }
                        self.auth_type = "pat"
                    else:
                        self.show_auth_status_dialog(
                            "Authentication Error",
                            "OAuth authentication failed and no PAT token available for fallback.",
                            ["Add a PAT token to your .env file"],
                            ""
                        )
            except Exception as e:
                # Use our custom dialog instead of messagebox
                self.show_auth_status_dialog(
                    "Authentication Warning",
                    f"Failed to get OAuth headers. Falling back to PAT authentication.",
                    ["Check your OAuth credentials and try again"],
                    f"Error details: {str(e)}"
                )

                # Fall back to PAT
                token = os.getenv("DATABRICKS_TOKEN", "").strip()
                if token:
                    self.headers = {
                        "Authorization": f"Bearer {token}",
                        "Content-Type": "application/json"
                    }
                    self.auth_type = "pat"
                else:
                    self.show_auth_status_dialog(
                        "Authentication Error",
                        "OAuth authentication failed and no PAT token available for fallback.",
                        ["Add a PAT token to your .env file"],
                        ""
                    )
                    self.headers = {}
        else:  # Default to PAT
            token = os.getenv("DATABRICKS_TOKEN", "").strip()

            # Print debug information
            print(f"Using PAT authentication with token: {token[:5]}...{token[-5:] if len(token) > 10 else ''}")

            self.headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            }

    def create_layout(self):
        """Create the main application layout"""
        # Create main frame with padding
        main_frame = ttk.Frame(self.root, padding="15", style="Main.TFrame")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Create header frame
        header_frame = ttk.Frame(main_frame, style="Header.TFrame")
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Create a frame for the logos
        logo_frame = ttk.Frame(header_frame, style="Header.TFrame")
        logo_frame.pack(fill=tk.X, pady=(0, 15))

        # Try to load and display Databricks logo
        databricks_logo = None
        try:
            # Try different possible logo files
            for logo_file in ["assets/databricks.png", "assets/databricks_logo.png"]:
                try:
                    databricks_logo = tk.PhotoImage(file=logo_file)
                    # Resize if needed
                    if databricks_logo.width() > 200:
                        ratio = databricks_logo.width() / 200
                        databricks_logo = databricks_logo.subsample(int(ratio), int(ratio))
                    break
                except:
                    continue
        except:
            databricks_logo = None

        # Try to load and display Tudip logo
        tudip_logo = None
        try:
            tudip_logo = tk.PhotoImage(file="assets/tudip.jpeg")
            # Resize if needed
            if tudip_logo and tudip_logo.width() > 100:
                ratio = tudip_logo.width() / 100
                tudip_logo = tudip_logo.subsample(int(ratio), int(ratio))
        except:
            try:
                # Try JPEG format if PIL is available
                if PIL_AVAILABLE:
                    tudip_img = Image.open("assets/tudip.jpeg")
                    # Resize to appropriate height
                    width, height = tudip_img.size
                    new_width = int(100 * width / height) if height > 0 else 100
                    tudip_img = tudip_img.resize((new_width, 100), Image.LANCZOS)
                    tudip_logo = ImageTk.PhotoImage(tudip_img)
            except:
                tudip_logo = None

        # Create a frame for the logos to be side by side
        logos_container = ttk.Frame(logo_frame, style="Header.TFrame")
        logos_container.pack(fill=tk.X)

        # Add Databricks logo on the left
        if databricks_logo:
            databricks_label = ttk.Label(logos_container, image=databricks_logo, style="Main.TLabel")
            databricks_label.image = databricks_logo  # Keep a reference
            databricks_label.pack(side=tk.LEFT, padx=(0, 20))
        else:
            # If logo can't be loaded, create a text-based logo
            databricks_text = ttk.Label(
                logos_container,
                text="DATABRICKS",
                font=("Segoe UI", 22, "bold"),
                foreground="#FF3621",
                style="Main.TLabel"
            )
            databricks_text.pack(side=tk.LEFT, padx=(0, 20))

        # Add Tudip logo on the right
        if tudip_logo:
            tudip_label = ttk.Label(logos_container, image=tudip_logo, style="Main.TLabel")
            tudip_label.image = tudip_logo  # Keep a reference
            tudip_label.pack(side=tk.RIGHT)
        else:
            # If logo can't be loaded, create a text-based logo
            tudip_text = ttk.Label(
                logos_container,
                text="TUDIP TECHNOLOGIES",
                font=("Segoe UI", 16, "bold"),
                foreground="#0066CC",
                style="Main.TLabel"
            )
            tudip_text.pack(side=tk.RIGHT)

        # Create title and connection info frame
        title_frame = ttk.Frame(header_frame, style="Header.TFrame")
        title_frame.pack(fill=tk.X, pady=(10, 0))

        # Create title
        title_label = ttk.Label(
            title_frame,
            text="Databricks Genie",
            font=("Segoe UI", 20, "bold"),
            style="Main.TLabel"
        )
        title_label.pack(side=tk.LEFT)

        # Create connection info
        connection_text = f"Connected to: {self.host}" if self.host else "Not connected"
        connection_label = ttk.Label(
            title_frame,
            text=connection_text,
            font=("Segoe UI", 10),
            style="Main.TLabel"
        )
        connection_label.pack(side=tk.RIGHT, padx=5)

        # Create auth info with switch button
        auth_frame = ttk.Frame(header_frame, style="Header.TFrame")
        auth_frame.pack(side=tk.RIGHT, padx=5)

        # Create a badge-style label for auth type
        auth_badge_frame = ttk.Frame(auth_frame, style="Header.TFrame")
        auth_badge_frame.pack(side=tk.LEFT, padx=(0, 10))

        # Create a badge with the current auth type
        auth_badge_style = "auth-badge-oauth" if self.auth_type == "oauth" else "auth-badge-pat"
        auth_text = f"Auth: {self.auth_type.upper()}"
        auth_label = ttk.Label(
            auth_badge_frame,
            text=auth_text,
            style="Auth.TLabel"
        )
        auth_label.pack(side=tk.LEFT, padx=(0, 5))

        # Add button to switch authentication methods
        switch_text = "Switch to PAT" if self.auth_type == "oauth" else "Switch to OAuth"
        switch_auth_button = ttk.Button(
            auth_frame,
            text=switch_text,
            style="Secondary.TButton",
            command=self.switch_authentication_method
        )
        switch_auth_button.pack(side=tk.LEFT)

        # Create conversation frame
        conversation_frame = ttk.LabelFrame(
            main_frame,
            text="Conversation",
            padding="15",
            style="Conversation.TLabelframe"
        )
        conversation_frame.pack(fill=tk.BOTH, expand=True, pady=10)

        # Create conversation display with custom styling
        self.conversation_display = scrolledtext.ScrolledText(
            conversation_frame,
            wrap=tk.WORD,
            width=80,
            height=20,
            font=("Segoe UI", 11),
            background="#ffffff",
            borderwidth=0,
            highlightthickness=0,
            padx=15,
            pady=15
        )
        self.conversation_display.pack(fill=tk.BOTH, expand=True)
        self.conversation_display.config(state=tk.DISABLED)

        # Configure text tags for formatting
        self.conversation_display.tag_configure("user",
                                              foreground="#0D47A1",
                                              font=("Segoe UI", 11, "bold"))
        self.conversation_display.tag_configure("genie",
                                              foreground="#FF3621",
                                              font=("Segoe UI", 11, "bold"))
        self.conversation_display.tag_configure("system",
                                              foreground="#757575",
                                              font=("Segoe UI", 11, "italic"))
        self.conversation_display.tag_configure("code",
                                              font=("Consolas", 10),
                                              background="#F5F5F5",
                                              spacing1=8,
                                              spacing3=8)

        # Create input frame
        input_frame = ttk.Frame(main_frame, padding="15", style="Controls.TFrame")
        input_frame.pack(fill=tk.X, pady=(20, 10))

        # Create message input with placeholder
        self.message_var = tk.StringVar()
        self.message_entry = ttk.Entry(
            input_frame,
            textvariable=self.message_var,
            width=70,
            font=("Segoe UI", 12),
            style="TEntry"
        )
        self.message_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15))

        # Add placeholder text
        self.message_entry.insert(0, "Ask a question about your data...")
        self.message_entry.bind("<FocusIn>", lambda event: self.on_entry_focus_in(event, self.message_entry))
        self.message_entry.bind("<FocusOut>", lambda event: self.on_entry_focus_out(event, self.message_entry))
        self.message_entry.bind("<Return>", lambda _: self.send_message())

        # Create send button
        self.send_button = ttk.Button(
            input_frame,
            text="Send",
            command=self.send_message,
            style="Send.TButton"
        )
        self.send_button.pack(side=tk.RIGHT)

        # Create buttons frame
        buttons_frame = ttk.Frame(main_frame, padding="10", style="Controls.TFrame")
        buttons_frame.pack(fill=tk.X, pady=10)

        # Create new conversation button
        new_conv_button = ttk.Button(
            buttons_frame,
            text="New Conversation",
            command=self.start_new_conversation,
            style="Action.TButton"
        )
        new_conv_button.pack(side=tk.LEFT, padx=5)

        # Create clear button
        clear_button = ttk.Button(
            buttons_frame,
            text="Clear Display",
            command=self.clear_display,
            style="Secondary.TButton"
        )
        clear_button.pack(side=tk.LEFT, padx=5)

        # Create authentication switching buttons
        auth_frame = ttk.Frame(buttons_frame, style="Controls.TFrame")
        auth_frame.pack(side=tk.LEFT, padx=20)

        # Create a label for authentication
        auth_label = ttk.Label(
            auth_frame,
            text="Authentication:",
            font=("Segoe UI", 10),
            style="Main.TLabel"
        )
        auth_label.pack(side=tk.LEFT, padx=(0, 5))

        # Create switch to PAT button (only show if currently using OAuth)
        if self.auth_type == "oauth":
            switch_pat_button = ttk.Button(
                auth_frame,
                text="Switch to PAT",
                command=self.switch_to_pat,
                style="Secondary.TButton"
            )
            switch_pat_button.pack(side=tk.LEFT, padx=5)

        # Create switch to OAuth button (only show if currently using PAT)
        if self.auth_type == "pat":
            switch_oauth_button = ttk.Button(
                auth_frame,
                text="Switch to OAuth",
                command=self.switch_to_oauth,
                style="Secondary.TButton"
            )
            switch_oauth_button.pack(side=tk.LEFT, padx=5)

        # Create documentation button
        docs_button = ttk.Button(
            buttons_frame,
            text="Documentation",
            command=self.open_documentation,
            style="Secondary.TButton"
        )
        docs_button.pack(side=tk.RIGHT, padx=5)

        # Create settings button
        settings_button = ttk.Button(
            buttons_frame,
            text="Settings",
            command=self.open_settings,
            style="Secondary.TButton"
        )
        settings_button.pack(side=tk.RIGHT, padx=5)

        # Create a separator
        separator = ttk.Separator(main_frame, orient="horizontal")
        separator.pack(fill=tk.X, pady=(15, 10))

        # Create footer frame
        footer_frame = ttk.Frame(main_frame, style="Footer.TFrame")
        footer_frame.pack(fill=tk.X, pady=(0, 0))

        # Create inner padding frame
        inner_frame = ttk.Frame(footer_frame, style="Footer.TFrame")
        inner_frame.pack(fill=tk.X, padx=10, pady=10)

        # Left side - Status bar
        self.status_var = tk.StringVar()
        status_bar = ttk.Label(
            inner_frame,
            textvariable=self.status_var,
            style="StatusBar.TLabel",
            anchor=tk.W
        )
        status_bar.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Right side - Copyright info
        copyright_label = ttk.Label(
            inner_frame,
            text="© 2025 Tudip Technologies Pvt Ltd. All rights reserved.",
            style="Footer.TLabel"
        )
        copyright_label.pack(side=tk.RIGHT)

        # Set focus to message entry
        self.message_entry.focus_set()

    def on_entry_focus_in(self, event, entry):
        """Handle focus in event for the message entry"""
        if entry.get() == "Ask a question about your data...":
            entry.delete(0, tk.END)

    def on_entry_focus_out(self, event, entry):
        """Handle focus out event for the message entry"""
        if not entry.get():
            entry.insert(0, "Ask a question about your data...")

    def open_documentation(self):
        """Open the Databricks Genie API documentation"""
        webbrowser.open("https://docs.databricks.com/api/workspace/genie")

    def show_auth_status_dialog(self, title, message, checklist_items, footer_text):
        """Show a custom dialog for authentication status with a checklist"""
        # Create a new toplevel window as a direct child of the root window
        # This prevents nested dialog issues
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("550x450")
        dialog.minsize(450, 350)

        # Make the dialog transient and grab focus
        dialog.transient(self.root)
        dialog.grab_set()

        # Ensure dialog is created as a new window, not a child of another dialog
        try:
            # This works on Linux/X11
            dialog.attributes('-type', 'dialog')
        except:
            # Fallback for other platforms
            pass

        # Set background color
        dialog.configure(bg="#f8f9fa")

        # Create main frame with padding
        main_frame = ttk.Frame(dialog, padding="20", style="Main.TFrame")
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Create icon and message
        icon_frame = ttk.Frame(main_frame, style="Main.TFrame")
        icon_frame.pack(fill=tk.X, pady=(0, 15))

        # Use a Unicode character as an icon
        if "Error" in title or "Denied" in title:
            icon_text = "⚠️"  # Warning icon
            icon_color = "#dc3545"  # Red for errors
        else:
            icon_text = "ℹ️"  # Information icon
            icon_color = "#0062cc"  # Blue for info

        icon_label = ttk.Label(
            icon_frame,
            text=icon_text,
            font=("Helvetica", 28),
            background="#f8f9fa",
            foreground=icon_color
        )
        icon_label.pack(side=tk.LEFT, padx=(0, 15))

        # Message
        message_label = ttk.Label(
            icon_frame,
            text=message,
            font=("Helvetica", 12),
            background="#f8f9fa",
            foreground="#212529",
            wraplength=400
        )
        message_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Create checklist frame
        checklist_frame = ttk.LabelFrame(
            main_frame,
            text="Please verify:",
            padding="15",
            style="Conversation.TLabelframe"
        )
        checklist_frame.pack(fill=tk.X, pady=15)

        # Add checklist items
        for i, item in enumerate(checklist_items):
            item_frame = ttk.Frame(checklist_frame, style="Conversation.TLabelframe")
            item_frame.pack(fill=tk.X, pady=5)

            bullet_label = ttk.Label(
                item_frame,
                text="•",
                font=("Helvetica", 14, "bold"),
                background="#ffffff",
                foreground="#0062cc"
            )
            bullet_label.pack(side=tk.LEFT, padx=(0, 8))

            item_label = ttk.Label(
                item_frame,
                text=item,
                font=("Helvetica", 11),
                background="#ffffff",
                foreground="#212529",
                wraplength=400
            )
            item_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Footer text with API error details
        if footer_text:
            footer_frame = ttk.Frame(main_frame, style="Main.TFrame")
            footer_frame.pack(fill=tk.X, pady=(15, 0))

            # Split the footer text to extract API error details if present
            api_error_details = None
            main_footer_text = footer_text

            if "Error details:" in footer_text:
                parts = footer_text.split("Error details:", 1)
                main_footer_text = parts[0].strip()
                if len(parts) > 1:
                    api_error_details = parts[1].strip()

            # Main footer text
            footer_label = ttk.Label(
                footer_frame,
                text=main_footer_text,
                font=("Helvetica", 10),
                background="#f8f9fa",
                foreground="#495057",
                wraplength=500,
                justify=tk.LEFT
            )
            footer_label.pack(fill=tk.X)

            # API error details in a scrolled text if present
            if api_error_details:
                error_frame = ttk.LabelFrame(
                    main_frame,
                    text="API Error Details",
                    padding="10",
                    style="Conversation.TLabelframe"
                )
                error_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))

                error_text = scrolledtext.ScrolledText(
                    error_frame,
                    wrap=tk.WORD,
                    width=50,
                    height=5,
                    font=("Courier New", 9),
                    background="#f8f9fa",
                    foreground="#dc3545"
                )
                error_text.pack(fill=tk.BOTH, expand=True)
                error_text.insert(tk.END, api_error_details)
                error_text.config(state=tk.DISABLED)

        # OK button
        button_frame = ttk.Frame(main_frame, style="Main.TFrame")
        button_frame.pack(fill=tk.X, pady=(15, 0))

        ok_button = ttk.Button(
            button_frame,
            text="OK",
            command=dialog.destroy,
            style="Action.TButton",
            width=10
        )
        ok_button.pack(side=tk.RIGHT)

        # Center the dialog on the parent window
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() - dialog.winfo_width()) // 2
        y = self.root.winfo_y() + (self.root.winfo_height() - dialog.winfo_height()) // 2
        dialog.geometry(f"+{x}+{y}")

        # Set focus to the OK button
        ok_button.focus_set()

        # Make dialog modal
        dialog.focus_set()
        dialog.wait_window()

    def append_to_conversation(self, message, sender="You"):
        """Append a message to the conversation display"""
        self.conversation_display.config(state=tk.NORMAL)

        # Add timestamp
        timestamp = time.strftime("%H:%M:%S")

        # Add separator if not the first message
        if self.conversation_display.get("1.0", tk.END).strip():
            self.conversation_display.insert(tk.END, "\n" + "-" * 80 + "\n\n")

        # Add sender with appropriate formatting and timestamp
        if sender == "You":
            self.conversation_display.insert(tk.END, f"{sender} [{timestamp}]:\n", "user")
        elif sender == "Genie":
            self.conversation_display.insert(tk.END, f"{sender} [{timestamp}]:\n", "genie")
        else:
            self.conversation_display.insert(tk.END, f"{sender} [{timestamp}]:\n", "system")

        # Check if message contains SQL code and format it
        if "```sql" in message or "```SQL" in message:
            parts = message.split("```")
            for i, part in enumerate(parts):
                if i % 2 == 0:  # Regular text
                    self.conversation_display.insert(tk.END, part)
                else:  # Code block
                    if part.startswith("sql") or part.startswith("SQL"):
                        sql_code = part[3:].strip()  # Remove 'sql' prefix
                        self.conversation_display.insert(tk.END, "\n--- SQL Query ---\n", "code")
                        self.conversation_display.insert(tk.END, sql_code + "\n", "code")
                        self.conversation_display.insert(tk.END, "----------------\n")
                    else:
                        self.conversation_display.insert(tk.END, part)
        else:
            # Add message
            self.conversation_display.insert(tk.END, message)

        # Add a newline at the end
        self.conversation_display.insert(tk.END, "\n\n")

        # Scroll to the end
        self.conversation_display.see(tk.END)
        self.conversation_display.config(state=tk.DISABLED)

    def send_message(self):
        """Send a message to Genie API"""
        print("send_message function called")
        message = self.message_var.get().strip()
        print(f"Message content: '{message}'")
        if not message:
            print("Message is empty, returning")
            return

        # Reload configuration to ensure we have the latest credentials
        self.load_config()

        # Re-test authentication with the latest credentials
        self.test_authentication()

        # Check if authentication is successful
        if not hasattr(self, 'auth_successful') or not self.auth_successful:
            error_msg = (
                "Authentication Required\n\n"
                "You need to authenticate before sending messages.\n"
                "Please use the authentication switching buttons to try a different authentication method."
            )
            self.append_to_conversation(error_msg, "System")
            # Create a custom dialog instead of using messagebox to avoid window path issues
            self.show_auth_status_dialog(
                "Authentication Required",
                "You need to authenticate before sending messages.",
                ["Try a different authentication method using the buttons below"],
                ""
            )
            return

        # Clear the input field
        self.message_var.set("")

        # Display the message
        self.append_to_conversation(message, "You")

        # Add to message history to track for echo detection
        self.message_history.append(message)

        # Update status
        self.status_var.set("Sending message...")
        self.root.update()

        try:
            # Start a new conversation if needed
            if not self.conversation_id:
                self.start_conversation(message)
            else:
                self.send_to_conversation(message)
        except Exception as e:
            error_msg = f"Failed to send message: {str(e)}"
            self.append_to_conversation(error_msg, "System")
            messagebox.showerror("Error", error_msg)
            self.status_var.set("Error sending message")

    def start_conversation(self, message):
        """Start a new conversation with Genie"""
        # Check if authentication is successful
        if not hasattr(self, 'auth_successful') or not self.auth_successful:
            error_msg = (
                "Authentication Required\n\n"
                "You need to authenticate before starting a conversation.\n"
                "Please use the authentication switching buttons to try a different authentication method."
            )
            self.append_to_conversation(error_msg, "System")
            self.status_var.set("Authentication required")
            return

        # For OAuth authentication, we need to handle things differently
        if self.auth_type == "oauth":
            try:
                # Import the OAuth URL functions
                from oauth_auth import get_start_conversation_url

                # Get the correct URL for starting a conversation
                endpoint = get_start_conversation_url(self.space_id)

                # Print debug information
                print(f"Starting OAuth conversation with URL: {endpoint}")
                print(f"Headers: {self.headers}")
                print(f"Payload: {{'content': '{message}'}}")

                payload = {"content": message}

                # Update status
                self.status_var.set("Starting conversation with OAuth...")
                self.root.update()
            except ImportError:
                # Fall back to the old method if the OAuth module is not available
                endpoint = f"https://{self.host}/api/2.0/genie/spaces/{self.space_id}/start-conversation"

                # Add organization ID as a query parameter if available
                if hasattr(self, 'org_id') and self.org_id:
                    endpoint += f"?o={self.org_id}"

                # Print debug information
                print(f"Starting OAuth conversation with URL (fallback): {endpoint}")
                print(f"Headers: {self.headers}")
                print(f"Payload: {{'content': '{message}'}}")

                payload = {"content": message}

                # Update status
                self.status_var.set("Starting conversation with OAuth (fallback)...")
                self.root.update()

            try:
                # Send the request with timeout
                response = requests.post(endpoint, headers=self.headers, json=payload, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    self.conversation_id = data.get("conversation_id")
                    message_id = data.get("message_id")

                    # Wait for the message to complete
                    self.wait_for_message(message_id)
                elif response.status_code == 403:
                    # Handle permission denied error specifically
                    error_msg = "Permission Denied: Your OAuth credentials don't have access to this Genie space.\n\n"
                    error_msg += "Please make sure:\n"
                    error_msg += "1. The service principal has been granted access to this specific Genie space\n"
                    error_msg += "2. The Space ID is correct\n"
                    error_msg += "3. Try using PAT authentication instead if OAuth continues to fail\n\n"

                    self.append_to_conversation(error_msg, "System")
                    self.status_var.set("Permission Denied (403)")

                    # Show a message box with the error
                    messagebox.showerror("Permission Denied (403)",
                                        "Your OAuth credentials don't have access to this Genie space.\n\n"
                                        "Please make sure your service principal has been granted access to this Genie space.")
                else:
                    error_msg = f"Failed to start conversation: {response.status_code} - {response.text}"
                    self.append_to_conversation(error_msg, "System")
                    self.status_var.set(f"Error starting conversation: {response.status_code}")
            except Exception as e:
                error_msg = f"Error with OAuth conversation: {str(e)}"
                self.append_to_conversation(error_msg, "System")
                self.status_var.set("OAuth Error")
            return
        else:
            # For PAT, use the API URL format
            endpoint = f"https://{self.host}/api/2.0/genie/spaces/{self.space_id}/start-conversation"

            # Add organization ID as a query parameter if available
            if hasattr(self, 'org_id') and self.org_id:
                endpoint += f"?o={self.org_id}"

            # Print debug information
            print(f"Starting conversation with URL: {endpoint}")
            print(f"Headers: {self.headers}")
            print(f"Payload: {{'content': '{message}'}}")

            payload = {"content": message}

            # Update status
            self.status_var.set("Starting conversation...")
            self.root.update()

            try:
                # Send the request with timeout
                print(payload)
                response = requests.post(endpoint, headers=self.headers, json=payload, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    self.conversation_id = data.get("conversation_id")
                    message_id = data.get("message_id")

                    # Wait for the message to complete
                    self.wait_for_message(message_id)
                elif response.status_code == 403:
                    # Handle permission denied error specifically
                    error_msg = "Permission Denied: Your credentials don't have access to this Genie space.\n\n"
                    error_msg += "Please make sure:\n"
                    error_msg += "1. Your token has been granted access to this specific Genie space\n"
                    error_msg += "2. The Space ID is correct\n"

                    self.append_to_conversation(error_msg, "System")
                    self.status_var.set("Permission Denied")

                    # Show a message box with the error
                    messagebox.showerror("Permission Denied",
                                        "Your credentials don't have access to this Genie space.\n\n"
                                        "Please make sure your token has been granted access to this Genie space.")
                else:
                    error_msg = f"Failed to start conversation: {response.status_code} - {response.text}"
                    self.append_to_conversation(error_msg, "System")
                    self.status_var.set("Error starting conversation")
            except requests.exceptions.Timeout:
                error_msg = "Request timed out while starting conversation"
                self.append_to_conversation(error_msg, "System")
                self.status_var.set("Request timed out")
            except requests.exceptions.RequestException as e:
                error_msg = f"Request error while starting conversation: {str(e)}"
                self.append_to_conversation(error_msg, "System")
                self.status_var.set("Request error")
            except Exception as e:
                error_msg = f"Unexpected error while starting conversation: {str(e)}"
                self.append_to_conversation(error_msg, "System")
                self.status_var.set("Error")

    def send_to_conversation(self, message):
        """Send a message to an existing conversation"""
        # Check if authentication is successful
        if not hasattr(self, 'auth_successful') or not self.auth_successful:
            error_msg = (
                "Authentication Required\n\n"
                "You need to authenticate before sending messages.\n"
                "Please use the authentication switching buttons to try a different authentication method."
            )
            self.append_to_conversation(error_msg, "System")
            self.status_var.set("Authentication required")
            return

        # For OAuth authentication, we need to handle things differently
        if self.auth_type == "oauth":
            try:
                # Import the OAuth URL functions
                from oauth_auth import get_send_message_url

                # Get the correct URL for sending a message to a conversation
                endpoint = get_send_message_url(self.conversation_id, self.space_id)

                # Print debug information
                print(f"Sending OAuth message to conversation with URL: {endpoint}")
                print(f"Headers: {self.headers}")
                print(f"Payload: {{'content': '{message}'}}")

                payload = {"content": message}

                # Update status
                self.status_var.set("Sending message with OAuth...")
                self.root.update()
            except ImportError:
                # Fall back to the old method if the OAuth module is not available
                endpoint = f"https://{self.host}/api/2.0/genie/spaces/{self.space_id}/conversations/{self.conversation_id}/messages"

                # Add organization ID as a query parameter if available
                if hasattr(self, 'org_id') and self.org_id:
                    endpoint += f"?o={self.org_id}"

                # Print debug information
                print(f"Sending OAuth message to conversation with URL (fallback): {endpoint}")
                print(f"Headers: {self.headers}")
                print(f"Payload: {{'content': '{message}'}}")

                payload = {"content": message}

                # Update status
                self.status_var.set("Sending message with OAuth (fallback)...")
                self.root.update()

            try:
                # Send the request with timeout
                response = requests.post(endpoint, headers=self.headers, json=payload, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    message_id = data.get("message_id")

                    # Wait for the message to complete
                    self.wait_for_message(message_id)
                elif response.status_code == 403:
                    # Handle permission denied error specifically
                    error_msg = "Permission Denied: Your OAuth credentials don't have access to this Genie space.\n\n"
                    error_msg += "Please make sure:\n"
                    error_msg += "1. The service principal has been granted access to this specific Genie space\n"
                    error_msg += "2. The Space ID is correct\n"
                    error_msg += "3. Try using PAT authentication instead if OAuth continues to fail\n\n"

                    self.append_to_conversation(error_msg, "System")
                    self.status_var.set("Permission Denied (403)")

                    # Use our custom dialog instead of messagebox to avoid window path issues
                    self.show_auth_status_dialog(
                        "Permission Denied (403)",
                        "Your OAuth credentials don't have access to this Genie space.",
                        [
                            "The service principal has been granted access to this specific Genie space",
                            "The Space ID is correct",
                            "Your OAuth credentials have the necessary permissions"
                        ],
                        "Try using PAT authentication instead if OAuth continues to fail."
                    )
                else:
                    error_msg = f"Failed to send message: {response.status_code} - {response.text}"
                    self.append_to_conversation(error_msg, "System")
                    self.status_var.set(f"Error sending message: {response.status_code}")
            except Exception as e:
                error_msg = f"Error with OAuth message: {str(e)}"
                self.append_to_conversation(error_msg, "System")
                self.status_var.set("OAuth Error")
            return
        else:
            # For PAT, use the API URL format
            endpoint = f"https://{self.host}/api/2.0/genie/spaces/{self.space_id}/conversations/{self.conversation_id}/messages"

            # Add organization ID as a query parameter if available
            if hasattr(self, 'org_id') and self.org_id:
                endpoint += f"?o={self.org_id}"

            # Print debug information
            print(f"Sending message to conversation with URL: {endpoint}")
            print(f"Headers: {self.headers}")
            print(f"Payload: {{'content': '{message}'}}")

            payload = {"content": message}

            # Update status
            self.status_var.set("Sending message...")
            self.root.update()

            try:
                # Send the request with timeout
                response = requests.post(endpoint, headers=self.headers, json=payload, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    message_id = data.get("message_id")

                    # Wait for the message to complete
                    self.wait_for_message(message_id)
                elif response.status_code == 403:
                    # Handle permission denied error specifically
                    error_msg = "Permission Denied: Your credentials don't have access to this Genie space.\n\n"
                    error_msg += "Please make sure:\n"
                    error_msg += "1. Your token has been granted access to this specific Genie space\n"
                    error_msg += "2. The Space ID is correct\n"

                    self.append_to_conversation(error_msg, "System")
                    self.status_var.set("Permission Denied")

                    # Use our custom dialog instead of messagebox to avoid window path issues
                    self.show_auth_status_dialog(
                        "Permission Denied",
                        "Your credentials don't have access to this Genie space.",
                        [
                            "Your token has been granted access to this specific Genie space",
                            "The Space ID is correct",
                            "Your token has the necessary permissions"
                        ],
                        "Check your token permissions and try again."
                    )
                else:
                    error_msg = f"Failed to send message: {response.status_code} - {response.text}"
                    self.append_to_conversation(error_msg, "System")
                    self.status_var.set("Error sending message")
            except requests.exceptions.Timeout:
                error_msg = "Request timed out while sending message"
                self.append_to_conversation(error_msg, "System")
                self.status_var.set("Request timed out")
            except requests.exceptions.RequestException as e:
                error_msg = f"Request error while sending message: {str(e)}"
                self.append_to_conversation(error_msg, "System")
                self.status_var.set("Request error")
            except Exception as e:
                error_msg = f"Unexpected error while sending message: {str(e)}"
                self.append_to_conversation(error_msg, "System")
                self.status_var.set("Error")

    def get_message_status(self, conversation_id, message_id):
        """Get the status of a message"""
        # For OAuth authentication, use the OAuth URL functions
        if self.auth_type == "oauth":
            try:
                # Import the OAuth URL functions
                from oauth_auth import get_message_url

                # Get the correct URL for getting a message
                endpoint = get_message_url(conversation_id, message_id, self.space_id)
            except ImportError:
                # Fall back to the old method if the OAuth module is not available
                endpoint = f"https://{self.host}/api/2.0/genie/spaces/{self.space_id}/conversations/{conversation_id}/messages/{message_id}"

                # Add organization ID as a query parameter if available
                if hasattr(self, 'org_id') and self.org_id:
                    endpoint += f"?o={self.org_id}"
        else:
            # For PAT, use the API URL format
            endpoint = f"https://{self.host}/api/2.0/genie/spaces/{self.space_id}/conversations/{conversation_id}/messages/{message_id}"

            # Add organization ID as a query parameter if available
            if hasattr(self, 'org_id') and self.org_id:
                endpoint += f"?o={self.org_id}"

        try:
            # Add a timeout to prevent hanging
            response = requests.get(endpoint, headers=self.headers, timeout=5)

            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error getting message status: {response.text}")
                return None
        except requests.exceptions.Timeout:
            print("Request timed out while getting message status")
            return None
        except requests.exceptions.RequestException as e:
            print(f"Request error while getting message status: {str(e)}")
            return None
        except Exception as e:
            print(f"Unexpected error while getting message status: {str(e)}")
            return None

    def get_query_result(self, conversation_id, message_id, attachment_id):
        """Get the query result for a message attachment"""
        # Use the API URL format for all authentication types
        endpoint = f"https://{self.host}/api/2.0/genie/spaces/{self.space_id}/conversations/{conversation_id}/messages/{message_id}/attachments/{attachment_id}/query-result"

        # Add organization ID as a query parameter if available
        if hasattr(self, 'org_id') and self.org_id:
            endpoint += f"?o={self.org_id}"

        try:
            # Add a timeout to prevent hanging
            response = requests.get(endpoint, headers=self.headers, timeout=5)

            if response.status_code == 200:
                return response.json()
            else:
                print(f"Error getting query result: {response.text}")
                return None
        except requests.exceptions.Timeout:
            print("Request timed out while getting query result")
            return None
        except requests.exceptions.RequestException as e:
            print(f"Request error while getting query result: {str(e)}")
            return None
        except Exception as e:
            print(f"Unexpected error while getting query result: {str(e)}")
            return None

    def wait_for_message(self, message_id):
        """Wait for a message to complete and get the response"""
        # Update status
        self.status_var.set("Waiting for Genie to process your question...")
        self.root.update()

        # Use adaptive polling with shorter intervals
        max_wait_seconds = 180  # 3 minutes timeout (reduced from 5)

        # Adaptive polling parameters
        current_poll_interval = 0.5  # Start with 0.5 second
        max_poll_interval = 2.0      # Max 2 seconds between polls
        poll_increase_factor = 1.2   # Increase by 20% each time

        start_time = time.time()
        animation_frames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
        frame_idx = 0

        while time.time() - start_time < max_wait_seconds:
            # Get message status
            message = self.get_message_status(self.conversation_id, message_id)

            if not message:
                self.append_to_conversation("Error getting message status", "System")
                self.status_var.set("Error getting message status")
                return

            status = message.get("status")

            if status == "COMPLETED":
                # Get the response content
                content = message.get("content", "No response content")
                actual_response = content

                # Check for attachments
                attachments = message.get("attachments", [])
                sql_query = None
                query_description = ""

                if attachments:
                    # Check for text attachment (actual response)
                    for attachment in attachments:
                        if attachment.get("text") and attachment.get("text").get("content"):
                            actual_response = attachment.get("text").get("content")
                            break

                    # Check for query attachment
                    for attachment in attachments:
                        if attachment.get("query"):
                            query_info = attachment.get("query", {})
                            sql_query = query_info.get("query")
                            query_description = query_info.get("description", "")
                            attachment_id = attachment.get("attachment_id")

                            # Get query result
                            try:
                                query_result = self.get_query_result(self.conversation_id, message_id, attachment_id)

                                # Display query info in a more readable format
                                if sql_query:
                                    query_text = "\n```sql\n"
                                    query_text += sql_query
                                    query_text += "\n```\n"
                                    self.append_to_conversation(query_text, "System")

                                # Display query description if available
                                if query_description:
                                    desc_text = "Query Description:\n"
                                    desc_text += query_description
                                    desc_text += "\n"
                                    self.append_to_conversation(desc_text, "System")

                                # Display query results
                                if query_result:
                                    data = query_result.get("data", [])
                                    if data:
                                        result_info = f"\nQuery Results: {len(data)} rows returned"
                                        self.append_to_conversation(result_info, "System")
                                else:
                                    self.append_to_conversation("\nNo results available.", "System")
                            except Exception as e:
                                self.append_to_conversation(f"\nError getting query result: {str(e)}", "System")
                            break

                # Display the actual response, but only if it's not just echoing the question
                original_question = message.get("content", "").strip()
                if actual_response.strip() != original_question:
                    self.append_to_conversation(actual_response, "Genie")

                self.status_var.set("Ready")
                return

            elif status == "FAILED":
                error_msg = message.get("error_message", "Unknown error")
                self.append_to_conversation(f"Error: {error_msg}", "System")
                self.status_var.set("Response failed")
                return

            # If still processing, update status with an animated indicator
            elapsed = int(time.time() - start_time)
            frame_idx = (frame_idx + 1) % len(animation_frames)
            animation_frame = animation_frames[frame_idx]

            # Update status more frequently with animation
            self.status_var.set(f"{animation_frame} Processing your question... ({elapsed}s)")
            self.root.update()

            # Adaptive sleep time
            time.sleep(current_poll_interval)

            # Gradually increase the polling interval
            current_poll_interval = min(current_poll_interval * poll_increase_factor, max_poll_interval)

        # If we get here, the message didn't complete in time
        self.append_to_conversation("Response timed out. The query may be too complex or the server may be busy.", "System")
        self.status_var.set("Response timed out")

    def start_new_conversation(self):
        """Start a new conversation"""
        # Check if authentication is successful
        if not hasattr(self, 'auth_successful') or not self.auth_successful:
            error_msg = (
                "Authentication Required\n\n"
                "You need to authenticate before starting a new conversation.\n"
                "Please use the authentication switching buttons to try a different authentication method."
            )
            self.append_to_conversation(error_msg, "System")
            # Use our custom dialog instead of messagebox to avoid window path issues
            self.show_auth_status_dialog(
                "Authentication Required",
                "You need to authenticate before starting a new conversation.",
                ["Try a different authentication method using the buttons below"],
                ""
            )
            return

        # Reset conversation ID
        self.conversation_id = None

        # Reset message history
        self.message_history = []

        # Clear the conversation display
        self.clear_display()

        # Update status
        self.status_var.set(f"Ready for a new conversation ({self.auth_type.upper()} authentication)")

        # Show a message
        self.append_to_conversation(f"Started a new conversation with {self.auth_type.upper()} authentication", "System")

    def clear_display(self):
        """Clear the conversation display"""
        self.conversation_display.config(state=tk.NORMAL)
        self.conversation_display.delete(1.0, tk.END)
        self.conversation_display.config(state=tk.DISABLED)

    def open_settings(self):
        """Open the settings dialog"""
        # For now, just open the existing auth_ui.py if it exists
        try:
            if os.path.exists("auth_ui.py"):
                # Run the auth_ui.py script
                import subprocess
                subprocess.Popen([sys.executable, "auth_ui.py"])
            else:
                # Use our custom dialog instead of messagebox to avoid window path issues
                self.show_auth_status_dialog(
                    "Settings",
                    "Settings are not available",
                    [],
                    ""
                )
        except Exception as e:
            # Use our custom dialog instead of messagebox to avoid window path issues
            self.show_auth_status_dialog(
                "Settings Error",
                f"Could not open settings",
                [],
                f"Error details: {str(e)}"
            )

    def switch_authentication_method(self):
        """Switch between PAT and OAuth authentication methods"""
        # Determine the new authentication method
        new_auth_type = "pat" if self.auth_type == "oauth" else "oauth"

        # Check if the required credentials are available
        if new_auth_type == "pat":
            token = os.getenv("DATABRICKS_TOKEN", "").strip()
            if not token:
                self.show_auth_status_dialog(
                    "Missing Credentials",
                    "No PAT token found in your .env file.",
                    ["Add a PAT token to your .env file"],
                    "You need to add a PAT token to your .env file before switching to PAT authentication."
                )
                return
        elif new_auth_type == "oauth":
            client_id = os.getenv("DATABRICKS_CLIENT_ID", "").strip()
            client_secret = os.getenv("DATABRICKS_CLIENT_SECRET", "").strip()
            if not client_id or not client_secret:
                self.show_auth_status_dialog(
                    "Missing Credentials",
                    "No OAuth credentials found in your .env file.",
                    ["Add OAuth client ID and secret to your .env file"],
                    "You need to add OAuth credentials to your .env file before switching to OAuth authentication."
                )
                return

        # Update the authentication type
        self.auth_type = new_auth_type

        # Reset conversation state to ensure a fresh start
        self.conversation_id = None
        self.message_history = []

        # Update the environment variable
        os.environ["CURRENT_AUTH_TYPE"] = new_auth_type

        # Save the new authentication type to the .env file with appropriate credentials
        from dotenv import set_key, find_dotenv, load_dotenv
        dotenv_path = find_dotenv()
        if dotenv_path:
            # First reload the .env file to get the latest values
            load_dotenv(dotenv_path, override=True)

            # Now update the authentication type
            if new_auth_type == "pat":
                # Save PAT settings and preserve OAuth credentials
                token = os.getenv("DATABRICKS_TOKEN", "").strip()
                client_id = os.getenv("DATABRICKS_CLIENT_ID", "").strip()
                client_secret = os.getenv("DATABRICKS_CLIENT_SECRET", "").strip()

                set_key(dotenv_path, "CURRENT_AUTH_TYPE", "pat", quote_mode="never")
                set_key(dotenv_path, "DATABRICKS_TOKEN", token, quote_mode="never")

                # Preserve OAuth credentials
                set_key(dotenv_path, "DATABRICKS_CLIENT_ID", client_id, quote_mode="never")
                set_key(dotenv_path, "DATABRICKS_CLIENT_SECRET", client_secret, quote_mode="never")

                print(f"Switched to PAT authentication with token: {token[:5]}...{token[-5:] if len(token) > 10 else ''}")
                print(f"Preserved OAuth credentials - Client ID: {client_id[:5]}...{client_id[-5:] if len(client_id) > 10 else ''}")
            else:  # oauth
                # Save OAuth settings and preserve PAT token
                token = os.getenv("DATABRICKS_TOKEN", "").strip()
                client_id = os.getenv("DATABRICKS_CLIENT_ID", "").strip()
                client_secret = os.getenv("DATABRICKS_CLIENT_SECRET", "").strip()

                set_key(dotenv_path, "CURRENT_AUTH_TYPE", "oauth", quote_mode="never")
                set_key(dotenv_path, "DATABRICKS_CLIENT_ID", client_id, quote_mode="never")
                set_key(dotenv_path, "DATABRICKS_CLIENT_SECRET", client_secret, quote_mode="never")

                # Preserve PAT token
                set_key(dotenv_path, "DATABRICKS_TOKEN", token, quote_mode="never")

                print(f"Switched to OAuth authentication with client ID: {client_id[:5]}...{client_id[-5:] if len(client_id) > 10 else ''}")
                print(f"Preserved PAT token: {token[:5]}...{token[-5:] if len(token) > 10 else ''}")

        # Show a message about the switch
        switch_msg = f"Switching to {new_auth_type.upper()} authentication...\n\nThe application will now reload with the new authentication method."
        self.append_to_conversation(switch_msg, "System")

        # Ensure the message entry and send button are properly set up
        self.message_entry.delete(0, tk.END)
        self.message_entry.insert(0, "Ask a question about your data...")
        self.message_entry.bind("<FocusIn>", lambda event: self.on_entry_focus_in(event, self.message_entry))
        self.message_entry.bind("<FocusOut>", lambda event: self.on_entry_focus_out(event, self.message_entry))
        self.message_entry.bind("<Return>", lambda _: self.send_message())

        # Ensure the send button is properly set up
        self.send_button.config(command=self.send_message)

        # Reload the configuration
        self.load_config()

        # Test the new authentication
        self.test_authentication()

        # Update the UI to reflect the new authentication method
        self.root.title(f"Databricks Genie - {new_auth_type.upper()} Authentication")

        # Restart the application with the new authentication method
        self.restart_application()

    def restart_application(self):
        """Restart the application with the current authentication method"""
        print("Restarting application with auth type:", self.auth_type)

        # Save the current auth type to an environment variable
        os.environ["CURRENT_AUTH_TYPE"] = self.auth_type

        # Instead of restarting, we'll destroy the current window and start fresh
        self.root.destroy()

        # Create a new root window
        root = tk.Tk()
        root.title(f"Databricks Genie - {self.auth_type.upper()} Authentication")
        root.geometry("800x600")  # Set a default size

        # Initialize the main application with the current authentication method
        # This ensures a completely fresh start
        app = GenieApp(root, auth_type=self.auth_type)

        # Start the main loop
        root.mainloop()

    def switch_to_pat(self):
        """Switch from OAuth to PAT authentication"""
        # Create a custom confirmation dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Switch Authentication")
        dialog.geometry("400x200")
        dialog.minsize(400, 200)
        dialog.transient(self.root)
        dialog.grab_set()
        try:
            # This works on Linux/X11
            dialog.attributes('-type', 'dialog')
        except:
            # Fallback for other platforms
            pass

        # Set background color
        dialog.configure(bg="#f8f9fa")

        # Create main frame
        main_frame = ttk.Frame(dialog, padding="20", style="Main.TFrame")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create message
        message_label = ttk.Label(
            main_frame,
            text="Are you sure you want to switch to PAT authentication?\n\nThis will close the current conversation and open the login UI.",
            font=("Helvetica", 11),
            background="#f8f9fa",
            foreground="#212529",
            wraplength=350,
            justify=tk.CENTER
        )
        message_label.pack(pady=20)

        # Create buttons frame
        button_frame = ttk.Frame(main_frame, style="Main.TFrame")
        button_frame.pack(fill=tk.X, pady=10)

        # Variable to track result
        result = [False]  # Use a list to allow modification from within the callback

        # Yes button
        yes_button = ttk.Button(
            button_frame,
            text="Yes",
            command=lambda: [result.append(True), dialog.destroy()],
            style="Action.TButton",
            width=10
        )
        yes_button.pack(side=tk.RIGHT, padx=5)

        # No button
        no_button = ttk.Button(
            button_frame,
            text="No",
            command=dialog.destroy,
            style="Action.TButton",
            width=10
        )
        no_button.pack(side=tk.RIGHT, padx=5)

        # Center the dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() - dialog.winfo_width()) // 2
        y = self.root.winfo_y() + (self.root.winfo_height() - dialog.winfo_height()) // 2
        dialog.geometry(f"+{x}+{y}")

        # Make dialog modal
        dialog.focus_set()
        dialog.wait_window()

        # Check result
        if not result[-1]:
            return

        # Close the current window
        self.root.destroy()

        # Launch the login UI with PAT tab selected
        try:
            root = tk.Tk()
            from login_ui import LoginUI
            login_ui = LoginUI(root)

            # Select the PAT tab
            login_ui.notebook.select(0)  # PAT tab is usually the first tab

            root.mainloop()
        except Exception as e:
            # Create a simple error dialog without using the class method
            # since we're already destroying the root window
            error_dialog = tk.Toplevel()
            error_dialog.title("Login Error")
            error_dialog.geometry("400x200")
            try:
                # This works on Linux/X11
                error_dialog.attributes('-type', 'dialog')
            except:
                # Fallback for other platforms
                pass

            # Set background color
            error_dialog.configure(bg="#f8f9fa")

            # Create main frame
            main_frame = tk.Frame(error_dialog, bg="#f8f9fa", padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Create message
            message_label = tk.Label(
                main_frame,
                text=f"Failed to launch login UI:\n\n{str(e)}",
                font=("Helvetica", 11),
                bg="#f8f9fa",
                fg="#dc3545",
                wraplength=350,
                justify=tk.CENTER
            )
            message_label.pack(pady=20)

            # Create OK button
            ok_button = tk.Button(
                main_frame,
                text="OK",
                command=error_dialog.destroy,
                width=10
            )
            ok_button.pack(pady=10)

            # Center the dialog
            error_dialog.update_idletasks()
            screen_width = error_dialog.winfo_screenwidth()
            screen_height = error_dialog.winfo_screenheight()
            x = (screen_width - error_dialog.winfo_width()) // 2
            y = (screen_height - error_dialog.winfo_height()) // 2
            error_dialog.geometry(f"+{x}+{y}")

            error_dialog.focus_set()
            error_dialog.wait_window()

    def switch_to_oauth(self):
        """Switch from PAT to OAuth authentication"""
        # Create a custom confirmation dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Switch Authentication")
        dialog.geometry("400x200")
        dialog.minsize(400, 200)
        dialog.transient(self.root)
        dialog.grab_set()
        try:
            # This works on Linux/X11
            dialog.attributes('-type', 'dialog')
        except:
            # Fallback for other platforms
            pass

        # Set background color
        dialog.configure(bg="#f8f9fa")

        # Create main frame
        main_frame = ttk.Frame(dialog, padding="20", style="Main.TFrame")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Create message
        message_label = ttk.Label(
            main_frame,
            text="Are you sure you want to switch to OAuth authentication?\n\nThis will close the current conversation and open the login UI.",
            font=("Helvetica", 11),
            background="#f8f9fa",
            foreground="#212529",
            wraplength=350,
            justify=tk.CENTER
        )
        message_label.pack(pady=20)

        # Create buttons frame
        button_frame = ttk.Frame(main_frame, style="Main.TFrame")
        button_frame.pack(fill=tk.X, pady=10)

        # Variable to track result
        result = [False]  # Use a list to allow modification from within the callback

        # Yes button
        yes_button = ttk.Button(
            button_frame,
            text="Yes",
            command=lambda: [result.append(True), dialog.destroy()],
            style="Action.TButton",
            width=10
        )
        yes_button.pack(side=tk.RIGHT, padx=5)

        # No button
        no_button = ttk.Button(
            button_frame,
            text="No",
            command=dialog.destroy,
            style="Action.TButton",
            width=10
        )
        no_button.pack(side=tk.RIGHT, padx=5)

        # Center the dialog
        dialog.update_idletasks()
        x = self.root.winfo_x() + (self.root.winfo_width() - dialog.winfo_width()) // 2
        y = self.root.winfo_y() + (self.root.winfo_height() - dialog.winfo_height()) // 2
        dialog.geometry(f"+{x}+{y}")

        # Make dialog modal
        dialog.focus_set()
        dialog.wait_window()

        # Check result
        if not result[-1]:
            return

        # Close the current window
        self.root.destroy()

        # Launch the login UI with OAuth tab selected
        try:
            root = tk.Tk()
            from login_ui import LoginUI
            login_ui = LoginUI(root)

            # Select the OAuth tab
            login_ui.notebook.select(1)  # OAuth tab is usually the second tab

            root.mainloop()
        except Exception as e:
            # Create a simple error dialog without using the class method
            # since we're already destroying the root window
            error_dialog = tk.Toplevel()
            error_dialog.title("Login Error")
            error_dialog.geometry("400x200")
            try:
                # This works on Linux/X11
                error_dialog.attributes('-type', 'dialog')
            except:
                # Fallback for other platforms
                pass

            # Set background color
            error_dialog.configure(bg="#f8f9fa")

            # Create main frame
            main_frame = tk.Frame(error_dialog, bg="#f8f9fa", padx=20, pady=20)
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Create message
            message_label = tk.Label(
                main_frame,
                text=f"Failed to launch login UI:\n\n{str(e)}",
                font=("Helvetica", 11),
                bg="#f8f9fa",
                fg="#dc3545",
                wraplength=350,
                justify=tk.CENTER
            )
            message_label.pack(pady=20)

            # Create OK button
            ok_button = tk.Button(
                main_frame,
                text="OK",
                command=error_dialog.destroy,
                width=10
            )
            ok_button.pack(pady=10)

            # Center the dialog
            error_dialog.update_idletasks()
            screen_width = error_dialog.winfo_screenwidth()
            screen_height = error_dialog.winfo_screenheight()
            x = (screen_width - error_dialog.winfo_width()) // 2
            y = (screen_height - error_dialog.winfo_height()) // 2
            error_dialog.geometry(f"+{x}+{y}")

            error_dialog.focus_set()
            error_dialog.wait_window()

def main():
    """Main function"""
    if not TKINTER_AVAILABLE:
        print("Cannot run the GUI application without tkinter.")
        print("Please install tkinter or use the command-line tools.")
        return

    root = tk.Tk()
    GenieApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
