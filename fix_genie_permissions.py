#!/usr/bin/env python3
"""
Fix Databricks Genie API Permissions for OAuth Authentication

This script helps diagnose and fix permission issues with the Databricks Genie API
when using OAuth authentication. It provides instructions on how to grant the
necessary permissions to your service principal.

Usage:
    python fix_genie_permissions.py
"""

import os
import json
import requests
import webbrowser
from dotenv import load_dotenv

# Import OAuth authentication module
try:
    from oauth_auth import get_auth_headers, get_oauth_token
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")  # PAT for admin operations
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")

# Check if required environment variables are set
if not DATABRICKS_HOST or not DATABRICKS_SPACE_ID:
    print("Error: Missing required environment variables.")
    print("Please set DATABRICKS_HOST and DATABRICKS_SPACE_ID in your .env file.")
    exit(1)

if not OAUTH_AVAILABLE or not DATABRICKS_CLIENT_ID or not DATABRICKS_CLIENT_SECRET:
    print("Error: OAuth authentication not available.")
    print("Please set DATABRICKS_CLIENT_ID and DATABRICKS_CLIENT_SECRET in your .env file.")
    exit(1)

if not DATABRICKS_TOKEN:
    print("Warning: No Personal Access Token (PAT) provided.")
    print("Some admin operations may not be available.")

# Extract the space ID and org ID
space_id_full = DATABRICKS_SPACE_ID
space_id = space_id_full.split('?')[0] if '?' in space_id_full else space_id_full
org_id = space_id_full.split('o=')[1] if 'o=' in space_id_full and '?' in space_id_full else None

# Remove any 'datarooms/' prefix if it exists
if space_id.startswith('datarooms/'):
    space_id = space_id.replace('datarooms/', '')

def get_pat_headers():
    """Get headers for PAT authentication"""
    return {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }

def check_oauth_token():
    """Check if the OAuth token is valid"""
    print("\n=== Checking OAuth Token ===")
    try:
        token = get_oauth_token()
        print(f"✅ Successfully obtained OAuth token: {token[:10]}...")
        return True
    except Exception as e:
        print(f"❌ Failed to get OAuth token: {str(e)}")
        return False

def test_standard_api():
    """Test OAuth token with a standard API call"""
    print("\n=== Testing Standard API Access ===")
    headers = get_auth_headers()
    url = f"https://{DATABRICKS_HOST}/api/2.0/clusters/list"

    try:
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            print(f"✅ Standard API call successful (Status: {response.status_code})")
            return True
        else:
            print(f"❌ Standard API call failed (Status: {response.status_code})")
            print(f"Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error with standard API call: {str(e)}")
        return False

def test_genie_api():
    """Test OAuth token with Genie API"""
    print("\n=== Testing Genie API Access ===")
    headers = get_auth_headers()

    # Try different URL formats for the Genie API
    url_formats = [
        # Format 1: Standard format
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}",

        # Format 2: With datarooms prefix
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}",

        # Format 3: Just datarooms
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms",

        # Format 4: List all spaces
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces"
    ]

    for i, url in enumerate(url_formats):
        print(f"\nTesting URL Format {i+1}: {url}")

        try:
            response = requests.get(url, headers=headers)

            print(f"Status Code: {response.status_code}")

            if response.status_code == 200:
                print(f"✅ Genie API call successful with format {i+1}!")
                print(f"Response: {json.dumps(response.json(), indent=2)[:200]}...")
                return True
            else:
                print(f"❌ Format {i+1} failed: {response.text[:200]}")

                # Check if it's a permission error
                if response.status_code == 403 and "PERMISSION_DENIED" in response.text:
                    print("⚠️ Permission denied error detected.")
        except Exception as e:
            print(f"❌ Error with format {i+1}: {str(e)}")

    print("\n⚠️ All URL formats failed. This suggests a permission issue.")
    print("The service principal needs 'Can View' permission on the Genie space.")
    return False

def get_service_principal_info():
    """Get information about the service principal"""
    print("\n=== Getting Service Principal Information ===")

    if not DATABRICKS_TOKEN:
        print("❌ Cannot get service principal info without a PAT token.")
        return None

    headers = get_pat_headers()
    url = f"https://{DATABRICKS_HOST}/api/2.0/preview/scim/v2/ServicePrincipals"

    try:
        response = requests.get(url, headers=headers)

        if response.status_code == 200:
            data = response.json()
            for sp in data.get("Resources", []):
                if sp.get("applicationId") == DATABRICKS_CLIENT_ID:
                    print(f"✅ Found service principal: {sp.get('displayName')}")
                    print(f"   ID: {sp.get('id')}")
                    print(f"   Application ID: {sp.get('applicationId')}")
                    return sp

            print("❌ Service principal not found with the given client ID.")
            return None
        else:
            print(f"❌ Failed to get service principals (Status: {response.status_code})")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error getting service principal info: {str(e)}")
        return None

def get_genie_space_info():
    """Get information about the Genie space"""
    print("\n=== Getting Genie Space Information ===")

    if not DATABRICKS_TOKEN:
        print("❌ Cannot get Genie space info without a PAT token.")
        return None

    headers = get_pat_headers()

    # Try different URL formats for the Genie API
    url_formats = [
        # Format 1: Standard format
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}",

        # Format 2: With datarooms prefix
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/datarooms/{space_id}",

        # Format 3: List all spaces
        f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces"
    ]

    for i, url in enumerate(url_formats):
        print(f"Trying URL Format {i+1}: {url}")

        try:
            response = requests.get(url, headers=headers)

            if response.status_code == 200:
                data = response.json()

                # For format 3 (list all spaces), find the space with matching ID
                if i == 2:  # Format 3 index
                    spaces = data.get("spaces", [])
                    for space in spaces:
                        if space.get("id") == space_id:
                            print(f"✅ Found Genie space: {space.get('name')}")
                            print(f"   ID: {space.get('id')}")
                            return space
                    print("❌ Space not found in the list of spaces.")
                else:
                    print(f"✅ Found Genie space: {data.get('name')}")
                    print(f"   ID: {data.get('id')}")
                    return data
            else:
                print(f"❌ Format {i+1} failed (Status: {response.status_code})")
                if response.text:
                    print(f"   Response: {response.text[:150]}...")
        except Exception as e:
            print(f"❌ Error with format {i+1}: {str(e)}")

    print("❌ Could not get Genie space info with any URL format.")
    return None

def provide_permission_instructions():
    """Provide instructions on how to grant permissions"""
    print("\n=== Permission Instructions ===")
    print("To grant 'Can View' permission to your service principal:")

    # Get service principal info
    sp_info = get_service_principal_info()
    space_info = get_genie_space_info()

    # Try to get a list of all spaces using PAT
    all_spaces = []
    if DATABRICKS_TOKEN:
        headers = get_pat_headers()
        url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces?o={org_id}" if org_id else f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces"

        try:
            response = requests.get(url, headers=headers)
            if response.status_code == 200:
                all_spaces = response.json().get("spaces", [])
                if all_spaces:
                    print(f"\n✅ Found {len(all_spaces)} Genie spaces in your workspace:")
                    for i, space in enumerate(all_spaces[:5]):  # Show up to 5 spaces
                        print(f"   {i+1}. {space.get('name', 'Unnamed')} (ID: {space.get('id', 'Unknown')})")
                    if len(all_spaces) > 5:
                        print(f"   ... and {len(all_spaces) - 5} more")
        except Exception as e:
            print(f"❌ Error listing all spaces: {str(e)}")

    if sp_info:
        sp_name = sp_info.get('displayName')
        sp_id = sp_info.get('id')

        print(f"\n1. Log in to your Databricks workspace: https://{DATABRICKS_HOST}")
        print("2. Navigate to the Genie section in the sidebar")

        if space_info:
            space_name = space_info.get('name')
            print(f"3. Find and click on the Genie space named '{space_name}'")
        elif all_spaces:
            print("3. Select one of the Genie spaces listed above")
        else:
            print("3. Find and click on your Genie space")

        print("4. Click the 'Share' button in the top-right corner")
        print(f"5. Search for the service principal named '{sp_name}' (ID: {sp_id})")
        print("6. Grant at least 'Can View' permission to the service principal")
        print("7. Click 'Save' to apply the permissions")

        # Open the workspace in a browser
        workspace_url = f"https://{DATABRICKS_HOST}/genie"
        print(f"\nOpening workspace in browser: {workspace_url}")
        try:
            webbrowser.open(workspace_url)
        except:
            print("Could not open browser automatically. Please open the URL manually.")
    else:
        print("\nCould not get detailed information about your service principal.")
        print("General instructions:")
        print(f"1. Log in to your Databricks workspace: https://{DATABRICKS_HOST}")
        print("2. Navigate to the Genie section in the sidebar")
        print("3. Find and click on your Genie space")
        print("4. Click the 'Share' button in the top-right corner")
        print("5. Search for your service principal (the one with client ID matching your OAuth credentials)")
        print("6. Grant at least 'Can View' permission to the service principal")
        print("7. Click 'Save' to apply the permissions")

    print("\nAdditional steps for workspace administrators:")
    print("1. Go to the Databricks workspace settings")
    print("2. Navigate to the 'Admin Console' section")
    print("3. Go to the 'Users and Groups' tab")
    print("4. Find your service principal")
    print("5. Ensure it has the necessary permissions to access Genie spaces")
    print("6. You may need to add the service principal to a group with appropriate permissions")

def main():
    """Main function"""
    print("=== Databricks Genie API Permission Fixer ===")
    print(f"Host: {DATABRICKS_HOST}")
    print(f"Space ID: {space_id}")
    print(f"Organization ID: {org_id}")

    # Check OAuth token
    if not check_oauth_token():
        print("\n❌ OAuth token is not valid. Please check your OAuth credentials.")
        return

    # Test standard API access
    if not test_standard_api():
        print("\n❌ Standard API access failed. Please check your OAuth credentials.")
        return

    # Test Genie API access
    if test_genie_api():
        print("\n✅ Genie API access is working correctly!")
        print("You have the necessary permissions to access the Genie space.")
        return

    # Provide permission instructions
    print("\n⚠️ Permission issue detected with Genie API access.")
    provide_permission_instructions()

    print("\nAfter granting permissions, run this script again to verify the fix.")

if __name__ == "__main__":
    main()
