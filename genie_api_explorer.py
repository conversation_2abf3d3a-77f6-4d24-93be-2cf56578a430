#!/usr/bin/env python3
"""
Databricks Genie API Explorer

This script explores the Databricks Genie API by making calls to various endpoints
and documenting the responses. It's designed to help understand how the API works
and what functionality it provides.

Important Notes:
    - The correct API path format is: /api/2.0/genie/spaces/{SPACE_ID}
    - Personal Access Token (PAT) authentication is more reliable than OAuth for Genie API
    - Make sure to remove any 'datarooms/' prefix from the Space ID

Usage:
    python genie_api_explorer.py

Requirements:
    - Python 3.6+
    - requests
    - python-dotenv
    - pandas (for displaying results)
"""

import os
import json
import time
import requests
import pandas as pd
from dotenv import load_dotenv
from datetime import datetime

# Import OAuth authentication module
try:
    from oauth_auth import get_auth_headers
    OAUTH_AVAILABLE = True
except ImportError:
    OAUTH_AVAILABLE = False

# Load environment variables from .env file
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")  # e.g., "dbc-123abc45-6def.cloud.databricks.com"
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")  # Your Databricks personal access token
DATABRICKS_SPACE_ID = os.getenv("DATABRICKS_SPACE_ID")  # Genie Space ID
DATABRICKS_CLIENT_ID = os.getenv("DATABRICKS_CLIENT_ID")  # OAuth client ID
DATABRICKS_CLIENT_SECRET = os.getenv("DATABRICKS_CLIENT_SECRET")  # OAuth client secret

# Check if required environment variables are set
if not DATABRICKS_HOST or not DATABRICKS_SPACE_ID:
    print("Error: Missing required environment variables.")
    print("Please create a .env file with DATABRICKS_HOST and DATABRICKS_SPACE_ID.")
    exit(1)

# Check authentication method
# Use PAT authentication as it's more reliable for Genie API
USE_OAUTH = False  # Use PAT instead of OAuth for Genie API
if not DATABRICKS_TOKEN:
    print("Error: No personal access token available.")
    print("Please provide a personal access token (DATABRICKS_TOKEN) in your .env file.")
    exit(1)

# API Base URL
# Extract the space ID without any query parameters
SPACE_ID = DATABRICKS_SPACE_ID.split('?')[0] if '?' in DATABRICKS_SPACE_ID else DATABRICKS_SPACE_ID
ORG_ID = None

# Check if there's an organization ID in the space ID
if '?' in DATABRICKS_SPACE_ID and 'o=' in DATABRICKS_SPACE_ID:
    ORG_ID = DATABRICKS_SPACE_ID.split('o=')[1] if 'o=' in DATABRICKS_SPACE_ID else None

# Remove any 'datarooms/' prefix if it exists in the SPACE_ID
if SPACE_ID.startswith('datarooms/'):
    SPACE_ID = SPACE_ID.replace('datarooms/', '')

# Construct the base URL without the endpoint - using the correct API path format
# Based on the successful format from previous logs
BASE_URL = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{SPACE_ID}"

# Headers for API requests
if USE_OAUTH:
    print("Using OAuth authentication")
    HEADERS = get_auth_headers()
else:
    print("Using Personal Access Token (PAT) authentication")
    HEADERS = {
        "Authorization": f"Bearer {DATABRICKS_TOKEN}",
        "Content-Type": "application/json"
    }

def log_request_response(func):
    """Decorator to log API requests and responses"""
    def wrapper(*args, **kwargs):
        print(f"\n{'='*80}")
        print(f"CALLING: {func.__name__}")
        print(f"{'='*80}")
        result = func(*args, **kwargs)
        return result
    return wrapper

@log_request_response
def start_conversation(question):
    """
    Start a new conversation with Genie

    Args:
        question (str): The initial question to ask

    Returns:
        dict: The API response containing conversation_id and message_id
    """
    # Construct the URL with the endpoint and query parameter if needed
    # Based on successful previous logs, the endpoint should be "/start-conversation"
    endpoint = "/start-conversation"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    payload = {"content": question}

    print(f"POST {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")

    response = requests.post(url, headers=HEADERS, json=payload)

    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    else:
        print(f"Error: {response.text}")
        return None

@log_request_response
def send_followup_message(conversation_id, question):
    """
    Send a follow-up message in an existing conversation

    Args:
        conversation_id (str): The ID of the conversation
        question (str): The follow-up question to ask

    Returns:
        dict: The API response containing the message details
    """
    # Construct the URL with the endpoint and query parameter if needed
    endpoint = f"/conversations/{conversation_id}/messages"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    payload = {"content": question}

    print(f"POST {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")

    response = requests.post(url, headers=HEADERS, json=payload)

    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    else:
        print(f"Error: {response.text}")
        return None

@log_request_response
def get_message_status(conversation_id, message_id):
    """
    Get the status of a message

    Args:
        conversation_id (str): The ID of the conversation
        message_id (str): The ID of the message

    Returns:
        dict: The API response containing the message details
    """
    # Construct the URL with the endpoint and query parameter if needed
    endpoint = f"/conversations/{conversation_id}/messages/{message_id}"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    print(f"GET {url}")

    response = requests.get(url, headers=HEADERS)

    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    else:
        print(f"Error: {response.text}")
        return None

@log_request_response
def get_query_result(conversation_id, message_id, attachment_id):
    """
    Get the query result for a message attachment

    Args:
        conversation_id (str): The ID of the conversation
        message_id (str): The ID of the message
        attachment_id (str): The ID of the attachment

    Returns:
        dict: The API response containing the query result
    """
    # Construct the URL with the endpoint and query parameter if needed
    endpoint = f"/conversations/{conversation_id}/messages/{message_id}/attachments/{attachment_id}/query-result"
    if ORG_ID:
        url = f"{BASE_URL}{endpoint}?o={ORG_ID}"
    else:
        url = f"{BASE_URL}{endpoint}"

    print(f"GET {url}")

    response = requests.get(url, headers=HEADERS)

    print(f"Status Code: {response.status_code}")
    if response.status_code == 200:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.json()
    else:
        print(f"Error: {response.text}")
        return None

def wait_for_message_completion(conversation_id, message_id, max_wait_seconds=300, poll_interval=5):
    """
    Wait for a message to complete processing

    Args:
        conversation_id (str): The ID of the conversation
        message_id (str): The ID of the message
        max_wait_seconds (int): Maximum time to wait in seconds
        poll_interval (int): Time between status checks in seconds

    Returns:
        dict: The completed message details or None if timed out
    """
    print(f"\nWaiting for message to complete (polling every {poll_interval} seconds, max {max_wait_seconds} seconds)...")

    start_time = time.time()
    while time.time() - start_time < max_wait_seconds:
        message = get_message_status(conversation_id, message_id)

        if not message:
            print("Failed to get message status")
            return None

        status = message.get("status")
        print(f"Current status: {status}")

        if status == "COMPLETED":
            print("Message processing completed!")
            return message
        elif status in ["FAILED", "ERROR"]:
            print(f"Message processing failed with status: {status}")
            return message

        print(f"Waiting {poll_interval} seconds before checking again...")
        time.sleep(poll_interval)

    print(f"Timed out after waiting {max_wait_seconds} seconds")
    return None

def run_api_exploration():
    """Run a series of API calls to explore the Genie API functionality"""
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    log_file = f"genie_api_exploration_{timestamp}.log"

    print(f"Starting Genie API exploration at {timestamp}")
    print(f"Results will be logged to {log_file}")

    # Redirect stdout to log file
    original_stdout = sys.stdout
    with open(log_file, 'w') as f:
        sys.stdout = f

        print(f"Databricks Genie API Exploration - {timestamp}")
        print(f"Host: {DATABRICKS_HOST}")
        print(f"Space ID: {DATABRICKS_SPACE_ID}")
        print(f"Authentication Method: {'OAuth' if USE_OAUTH else 'Personal Access Token (PAT)'}")

        # Test 1: Start a conversation with a simple question
        print("\n\n--- TEST 1: Start a conversation with a simple question ---")
        question = "Show me the top 10 customers by revenue"
        conversation = start_conversation(question)

        if not conversation:
            print("Failed to start conversation. Exiting.")
            sys.stdout = original_stdout
            return

        conversation_id = conversation.get("conversation_id")
        message_id = conversation.get("message_id")

        # Wait for the message to complete
        completed_message = wait_for_message_completion(conversation_id, message_id)

        if not completed_message:
            print("Message did not complete successfully. Exiting.")
            sys.stdout = original_stdout
            return

        # Get query results if available
        attachments = completed_message.get("attachments", [])
        if attachments:
            attachment_id = attachments[0].get("attachment_id")
            query_result = get_query_result(conversation_id, message_id, attachment_id)

        # Test 2: Send a follow-up question
        print("\n\n--- TEST 2: Send a follow-up question ---")
        followup_question = "Can you break this down by region?"
        followup_message = send_followup_message(conversation_id, followup_question)

        if not followup_message:
            print("Failed to send follow-up message. Exiting.")
            sys.stdout = original_stdout
            return

        followup_message_id = followup_message.get("message_id")

        # Wait for the follow-up message to complete
        completed_followup = wait_for_message_completion(conversation_id, followup_message_id)

        if not completed_followup:
            print("Follow-up message did not complete successfully. Exiting.")
            sys.stdout = original_stdout
            return

        # Get query results for follow-up if available
        followup_attachments = completed_followup.get("attachments", [])
        if followup_attachments:
            followup_attachment_id = followup_attachments[0].get("attachment_id")
            followup_query_result = get_query_result(conversation_id, followup_message_id, followup_attachment_id)

        print("\n\nAPI Exploration completed successfully!")

    # Restore stdout
    sys.stdout = original_stdout
    print(f"API Exploration completed. Results logged to {log_file}")

if __name__ == "__main__":
    import sys
    run_api_exploration()
