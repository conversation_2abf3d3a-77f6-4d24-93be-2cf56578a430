#!/usr/bin/env python3
"""
Test Databricks Personal Access Token (PAT) Authentication

This script tests the Databricks PAT authentication with various API endpoints,
including the Genie API.
"""

import os
import json
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")

# Check if required environment variables are set
if not DATABRICKS_HOST or not DATABRICKS_TOKEN:
    print("Error: Missing required environment variables.")
    print("Please set DATABRICKS_HOST and DATABRICKS_TOKEN in your .env file.")
    exit(1)

# Headers for API requests
HEADERS = {
    "Authorization": f"Bearer {DATABRICKS_TOKEN}",
    "Content-Type": "application/json"
}

def main():
    """Test PAT authentication with various API endpoints"""
    print(f"Testing PAT authentication for host: {DATABRICKS_HOST}")
    
    # Test standard API
    url = f"https://{DATABRICKS_HOST}/api/2.0/clusters/list"
    print(f"Testing PAT with standard API call to: {url}")
    
    try:
        response = requests.get(url, headers=HEADERS)
        
        if response.status_code == 200:
            print("Standard API call successful!")
            print(f"Response: {json.dumps(response.json(), indent=2)[:200]}...")
        else:
            print(f"Standard API call failed with status code {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error with standard API call: {str(e)}")
    
    # Test Genie API
    genie_url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces"
    print(f"\nTesting PAT with Genie API call to: {genie_url}")
    
    try:
        genie_response = requests.get(genie_url, headers=HEADERS)
        
        if genie_response.status_code == 200:
            print("Genie API call successful!")
            print(f"Response: {json.dumps(genie_response.json(), indent=2)}")
        else:
            print(f"Genie API call failed with status code {genie_response.status_code}")
            print(f"Response: {genie_response.text}")
    except Exception as e:
        print(f"Error with Genie API call: {str(e)}")
    
    # Try a specific Genie space
    space_id = "01f02f16a7b11b36a04e4353814a5699"
    org_id = "1883526265026134"
    space_url = f"https://{DATABRICKS_HOST}/api/2.0/genie/spaces/{space_id}?o={org_id}"
    
    print(f"\nTesting PAT with specific Genie space: {space_url}")
    
    try:
        space_response = requests.get(space_url, headers=HEADERS)
        
        if space_response.status_code == 200:
            print("Genie space API call successful!")
            print(f"Response: {json.dumps(space_response.json(), indent=2)}")
        else:
            print(f"Genie space API call failed with status code {space_response.status_code}")
            print(f"Response: {space_response.text}")
    except Exception as e:
        print(f"Error with Genie space API call: {str(e)}")

if __name__ == "__main__":
    main()
